<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Area - BadBoyz IPTV</title>
    <meta name="description" content="Access your BadBoyz IPTV client area to manage your subscription, download apps, and get support.">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="index.html">
                    <img src="assets/images/logo.png" alt="BadBoyz IPTV" class="logo">
                </a>
                
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="index.html">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Login Section -->
    <section class="login-section py-5" id="loginSection">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="login-card">
                        <div class="login-header text-center mb-4">
                            <i class="fas fa-user-circle login-icon"></i>
                            <h2>Client Area Login</h2>
                            <p>Access your account to manage your IPTV subscription</p>
                        </div>
                        
                        <form id="loginForm" class="login-form">
                            <div class="mb-3">
                                <label for="loginEmail" class="form-label">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="loginEmail" name="email" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="loginPassword" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="loginPassword" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="passwordToggle"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    Remember me
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </button>
                            </div>
                        </form>
                        
                        <div class="login-footer text-center mt-4">
                            <p><a href="#" onclick="showForgotPassword()">Forgot your password?</a></p>
                            <p>Don't have an account? <a href="subscriptions.html">Subscribe Now</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Section -->
    <section class="dashboard-section py-5" id="dashboardSection" style="display: none;">
        <div class="container">
            <!-- Welcome Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="welcome-header">
                        <h2>Welcome back, <span id="userName">John Doe</span>!</h2>
                        <p>Manage your IPTV subscription and account settings</p>
                        <button class="btn btn-outline-light" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </div>
            </div>

            <!-- Dashboard Stats -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-info">
                            <h4 id="subscriptionStatus">Active</h4>
                            <p>Subscription Status</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h4 id="daysRemaining">23</h4>
                            <p>Days Remaining</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <div class="stat-info">
                            <h4>16,000+</h4>
                            <p>Live Channels</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-film"></i>
                        </div>
                        <div class="stat-info">
                            <h4>40,000+</h4>
                            <p>VOD Content</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Dashboard Content -->
            <div class="row">
                <!-- Account Information -->
                <div class="col-lg-8 mb-4">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-user"></i> Account Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label>Email:</label>
                                        <span id="userEmail"><EMAIL></span>
                                    </div>
                                    <div class="info-item">
                                        <label>Plan:</label>
                                        <span id="userPlan">12 Month Subscription</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Start Date:</label>
                                        <span id="startDate">January 1, 2025</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label>Expiry Date:</label>
                                        <span id="expiryDate">January 1, 2026</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Connections:</label>
                                        <span>Up to 5 devices</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Status:</label>
                                        <span class="status-active">Active</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Connection Details -->
                    <div class="dashboard-card mt-4">
                        <div class="card-header">
                            <h3><i class="fas fa-link"></i> Connection Details</h3>
                        </div>
                        <div class="card-body">
                            <div class="connection-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <label>Username:</label>
                                            <span class="credential" id="iptvUsername">user123456</span>
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('iptvUsername')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <div class="info-item">
                                            <label>Password:</label>
                                            <span class="credential" id="iptvPassword">pass123456</span>
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('iptvPassword')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-item">
                                            <label>Server URL:</label>
                                            <span class="credential" id="serverUrl">http://server.streampro-iptv.com:8080</span>
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('serverUrl')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <div class="info-item">
                                            <label>M3U URL:</label>
                                            <span class="credential small" id="m3uUrl">http://server.streampro-iptv.com:8080/get.php?username=user123456&password=pass123456&type=m3u_plus</span>
                                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('m3uUrl')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-lg-4">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-download"></i> Download Apps</h3>
                        </div>
                        <div class="card-body">
                            <div class="app-downloads">
                                <a href="#" class="app-download-item">
                                    <i class="fab fa-android"></i>
                                    <div>
                                        <h5>IPTV Smarters Pro</h5>
                                        <p>For Android devices</p>
                                    </div>
                                    <i class="fas fa-download"></i>
                                </a>
                                
                                <a href="#" class="app-download-item">
                                    <i class="fab fa-apple"></i>
                                    <div>
                                        <h5>GSE Smart IPTV</h5>
                                        <p>For iOS devices</p>
                                    </div>
                                    <i class="fas fa-download"></i>
                                </a>
                                
                                <a href="#" class="app-download-item">
                                    <i class="fas fa-tv"></i>
                                    <div>
                                        <h5>Smart IPTV</h5>
                                        <p>For Smart TVs</p>
                                    </div>
                                    <i class="fas fa-download"></i>
                                </a>
                                
                                <a href="#" class="app-download-item">
                                    <i class="fas fa-desktop"></i>
                                    <div>
                                        <h5>VLC Player</h5>
                                        <p>For PC/Mac</p>
                                    </div>
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="dashboard-card mt-4">
                        <div class="card-header">
                            <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="quick-actions">
                                <button class="btn btn-primary btn-block mb-2">
                                    <i class="fas fa-sync"></i> Renew Subscription
                                </button>
                                <button class="btn btn-outline-primary btn-block mb-2">
                                    <i class="fas fa-edit"></i> Update Profile
                                </button>
                                <button class="btn btn-outline-primary btn-block mb-2">
                                    <i class="fas fa-key"></i> Change Password
                                </button>
                                <button class="btn btn-outline-primary btn-block mb-2">
                                    <i class="fas fa-headset"></i> Contact Support
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer py-3">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; 2025 StreamPro IPTV. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // Client Area Functions
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            // Simulate login (replace with actual authentication)
            if (email && password) {
                // Hide login section and show dashboard
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('dashboardSection').style.display = 'block';
                
                // Update user info
                document.getElementById('userName').textContent = email.split('@')[0];
                document.getElementById('userEmail').textContent = email;
                
                showNotification('Login successful!', 'success');
            } else {
                showNotification('Please enter valid credentials', 'error');
            }
        });
        
        function togglePassword() {
            const passwordInput = document.getElementById('loginPassword');
            const toggleIcon = document.getElementById('passwordToggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }
        
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                showNotification('Copied to clipboard!', 'success');
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('Copied to clipboard!', 'success');
            });
        }
        
        function logout() {
            document.getElementById('dashboardSection').style.display = 'none';
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('loginForm').reset();
            showNotification('Logged out successfully', 'info');
        }
        
        function showForgotPassword() {
            alert('Please contact our support <NAME_EMAIL> to reset your password.');
        }
        
        // Auto-login for demo purposes (remove in production)
        setTimeout(() => {
            if (window.location.hash === '#demo') {
                document.getElementById('loginEmail').value = '<EMAIL>';
                document.getElementById('loginPassword').value = 'demo123';
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        }, 1000);
    </script>
</body>
</html>
