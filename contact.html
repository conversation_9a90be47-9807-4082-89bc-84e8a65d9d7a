<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - StreamPro IPTV Support</title>
    <meta name="description" content="Get in touch with StreamPro IPTV support team. 24/7 customer service, live chat, email support, and comprehensive FAQ section.">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="index.html">
                    <img src="assets/images/logo.png" alt="StreamPro IPTV" class="logo">
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subscriptions.html">IPTV Subscription</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="free-trial.html">Free Trial</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reseller.html">IPTV Reseller</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tutorials.html">Installation Tutorial</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="about.html">About Us</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="contact.html">Contact Us</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-primary ms-2" href="client-area.html">Client Area</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title">Contact Us</h1>
                    <p class="page-subtitle">We're here to help you 24/7</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Methods -->
    <section class="contact-methods py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h4>Live Chat</h4>
                        <p>Get instant help from our support team</p>
                        <button class="btn btn-primary" onclick="openLiveChat()">
                            <i class="fas fa-comment-dots"></i> Start Chat
                        </button>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h4>Email Support</h4>
                        <p>Send us a detailed message</p>
                        <a href="mailto:<EMAIL>" class="btn btn-primary">
                            <i class="fas fa-envelope"></i> Send Email
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fab fa-telegram"></i>
                        </div>
                        <h4>Telegram</h4>
                        <p>Join our Telegram support channel</p>
                        <a href="https://t.me/streamproiptv" class="btn btn-primary" target="_blank">
                            <i class="fab fa-telegram"></i> Join Channel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="contact-form-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-form-card">
                        <div class="form-header text-center mb-4">
                            <h2>Send us a Message</h2>
                            <p>Fill out the form below and we'll get back to you within 24 hours</p>
                        </div>
                        
                        <form id="contactForm" class="contact-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="firstName" name="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="lastName" name="lastName" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subject" class="form-label">Subject *</label>
                                <select class="form-control" id="subject" name="subject" required>
                                    <option value="">Select a subject</option>
                                    <option value="technical-support">Technical Support</option>
                                    <option value="billing">Billing & Payment</option>
                                    <option value="subscription">Subscription Issues</option>
                                    <option value="reseller">Reseller Program</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="complaint">Complaint</option>
                                    <option value="suggestion">Suggestion</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select class="form-control" id="priority" name="priority">
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="5" placeholder="Please describe your issue or inquiry in detail..." required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="attachment" class="form-label">Attachment (Optional)</label>
                                <input type="file" class="form-control" id="attachment" name="attachment" accept=".jpg,.jpeg,.png,.pdf,.txt">
                                <small class="form-text text-muted">Max file size: 5MB. Allowed formats: JPG, PNG, PDF, TXT</small>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter for updates and special offers
                                </label>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane"></i> Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">Frequently Asked Questions</h2>
                    <p class="section-subtitle">Find quick answers to common questions</p>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="faq-item">
                                <div class="faq-question">
                                    <h5>How do I activate my subscription?</h5>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <p>After payment, you'll receive an email with your login credentials within 5-10 minutes. Use these credentials in your IPTV app to activate your service.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question">
                                    <h5>What if my channels are not working?</h5>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <p>First, check your internet connection. If the issue persists, try restarting your app or device. Contact our support team if problems continue.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question">
                                    <h5>Can I change my subscription plan?</h5>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <p>Yes, you can upgrade or downgrade your plan at any time. Contact our support team to make changes to your subscription.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question">
                                    <h5>Do you offer refunds?</h5>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <p>We offer a 7-day money-back guarantee. If you're not satisfied with our service, contact us within 7 days for a full refund.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6">
                            <div class="faq-item">
                                <div class="faq-question">
                                    <h5>How many devices can I use?</h5>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <p>Our subscriptions support up to 5 simultaneous connections, allowing you to watch on multiple devices at the same time.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question">
                                    <h5>Is there a setup fee?</h5>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <p>No, there are no setup fees or hidden charges. You only pay for your chosen subscription plan.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question">
                                    <h5>What payment methods do you accept?</h5>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <p>We accept PayPal, credit/debit cards, and various cryptocurrencies including Bitcoin, Ethereum, and USDT.</p>
                                </div>
                            </div>
                            
                            <div class="faq-item">
                                <div class="faq-question">
                                    <h5>Do you provide EPG (Electronic Program Guide)?</h5>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <p>Yes, we provide comprehensive EPG for most channels, showing current and upcoming programs with detailed information.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Support Hours -->
    <section class="support-hours py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h2 class="section-title">Support Hours</h2>
                    <div class="hours-grid">
                        <div class="hours-item">
                            <h4>Live Chat</h4>
                            <p>24/7 Available</p>
                        </div>
                        <div class="hours-item">
                            <h4>Email Support</h4>
                            <p>Response within 24 hours</p>
                        </div>
                        <div class="hours-item">
                            <h4>Telegram</h4>
                            <p>24/7 Community Support</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; 2025 StreamPro IPTV. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Live Chat Widget -->
    <div id="liveChatWidget" class="live-chat-widget">
        <button class="chat-button" onclick="toggleChat()" id="chatButton">
            <i class="fas fa-comments"></i>
            <span class="notification-badge" id="notificationBadge" style="display: none;">1</span>
        </button>

        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <div class="chat-header-content">
                    <div class="chat-avatar">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="chat-header-info">
                        <h5>Live Support</h5>
                        <p>We're online now</p>
                    </div>
                </div>
                <button class="close-chat" onclick="toggleChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="chat-body" id="chatBody">
                <div class="chat-message bot">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div>
                        <p>📞 Need immediate help? I'm here to assist you right away! What can I help you with?</p>
                        <div class="quick-replies">
                            <span class="quick-reply" onclick="sendQuickReply('I have a technical issue')">Technical Issue</span>
                            <span class="quick-reply" onclick="sendQuickReply('Billing question')">Billing</span>
                            <span class="quick-reply" onclick="sendQuickReply('General inquiry')">General</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-input">
                <input type="text" placeholder="Type your message..." id="chatInput" onkeypress="handleChatKeyPress(event)">
                <button class="chat-send-btn" onclick="sendMessage()" id="sendButton">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/livechat.js"></script>
</body>
</html>
