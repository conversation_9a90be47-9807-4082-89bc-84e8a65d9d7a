<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Simple file-based chat storage (for demo purposes)
// In production, use a proper database
$chatFile = 'chat_sessions.json';
$messagesDir = 'chat_messages/';

// Create directories if they don't exist
if (!file_exists($messagesDir)) {
    mkdir($messagesDir, 0755, true);
}

// Initialize chat sessions file
if (!file_exists($chatFile)) {
    file_put_contents($chatFile, json_encode([]));
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'start_session':
        startChatSession();
        break;
    case 'send_message':
        sendMessage();
        break;
    case 'get_messages':
        getMessages();
        break;
    case 'get_sessions':
        getSessions();
        break;
    case 'admin_reply':
        adminReply();
        break;
    default:
        echo json_encode(['error' => 'Invalid action']);
}

function startChatSession() {
    global $chatFile;
    
    $sessionId = uniqid('chat_', true);
    $visitorId = 'Visitor_' . substr($sessionId, -8);
    
    $sessions = json_decode(file_get_contents($chatFile), true);
    
    $newSession = [
        'session_id' => $sessionId,
        'visitor_id' => $visitorId,
        'start_time' => date('Y-m-d H:i:s'),
        'status' => 'active',
        'last_activity' => date('Y-m-d H:i:s'),
        'page' => $_POST['page'] ?? 'unknown',
        'unread_count' => 0
    ];
    
    $sessions[$sessionId] = $newSession;
    file_put_contents($chatFile, json_encode($sessions, JSON_PRETTY_PRINT));
    
    echo json_encode([
        'success' => true,
        'session_id' => $sessionId,
        'visitor_id' => $visitorId
    ]);
}

function sendMessage() {
    global $messagesDir, $chatFile;
    
    $sessionId = $_POST['session_id'] ?? '';
    $message = $_POST['message'] ?? '';
    $sender = $_POST['sender'] ?? 'customer'; // 'customer' or 'admin'
    
    if (empty($sessionId) || empty($message)) {
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }
    
    $messageFile = $messagesDir . $sessionId . '.json';
    $messages = [];
    
    if (file_exists($messageFile)) {
        $messages = json_decode(file_get_contents($messageFile), true);
    }
    
    $newMessage = [
        'id' => uniqid(),
        'message' => htmlspecialchars($message),
        'sender' => $sender,
        'timestamp' => date('Y-m-d H:i:s'),
        'time_display' => date('g:i A')
    ];
    
    $messages[] = $newMessage;
    file_put_contents($messageFile, json_encode($messages, JSON_PRETTY_PRINT));
    
    // Update session activity
    updateSessionActivity($sessionId, $sender);
    
    echo json_encode([
        'success' => true,
        'message' => $newMessage
    ]);
}

function getMessages() {
    global $messagesDir;
    
    $sessionId = $_GET['session_id'] ?? '';
    
    if (empty($sessionId)) {
        echo json_encode(['error' => 'Missing session_id']);
        return;
    }
    
    $messageFile = $messagesDir . $sessionId . '.json';
    $messages = [];
    
    if (file_exists($messageFile)) {
        $messages = json_decode(file_get_contents($messageFile), true);
    }
    
    echo json_encode([
        'success' => true,
        'messages' => $messages
    ]);
}

function getSessions() {
    global $chatFile;
    
    $sessions = json_decode(file_get_contents($chatFile), true);
    
    // Sort by last activity (most recent first)
    uasort($sessions, function($a, $b) {
        return strtotime($b['last_activity']) - strtotime($a['last_activity']);
    });
    
    echo json_encode([
        'success' => true,
        'sessions' => array_values($sessions)
    ]);
}

function adminReply() {
    global $messagesDir, $chatFile;
    
    $sessionId = $_POST['session_id'] ?? '';
    $message = $_POST['message'] ?? '';
    $adminName = $_POST['admin_name'] ?? 'Support Agent';
    
    if (empty($sessionId) || empty($message)) {
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }
    
    $messageFile = $messagesDir . $sessionId . '.json';
    $messages = [];
    
    if (file_exists($messageFile)) {
        $messages = json_decode(file_get_contents($messageFile), true);
    }
    
    $newMessage = [
        'id' => uniqid(),
        'message' => htmlspecialchars($message),
        'sender' => 'admin',
        'admin_name' => $adminName,
        'timestamp' => date('Y-m-d H:i:s'),
        'time_display' => date('g:i A')
    ];
    
    $messages[] = $newMessage;
    file_put_contents($messageFile, json_encode($messages, JSON_PRETTY_PRINT));
    
    // Update session activity and reset unread count
    updateSessionActivity($sessionId, 'admin', true);
    
    echo json_encode([
        'success' => true,
        'message' => $newMessage
    ]);
}

function updateSessionActivity($sessionId, $sender, $resetUnread = false) {
    global $chatFile;
    
    $sessions = json_decode(file_get_contents($chatFile), true);
    
    if (isset($sessions[$sessionId])) {
        $sessions[$sessionId]['last_activity'] = date('Y-m-d H:i:s');
        
        if ($sender === 'customer' && !$resetUnread) {
            $sessions[$sessionId]['unread_count'] = ($sessions[$sessionId]['unread_count'] ?? 0) + 1;
        } elseif ($resetUnread) {
            $sessions[$sessionId]['unread_count'] = 0;
        }
        
        file_put_contents($chatFile, json_encode($sessions, JSON_PRETTY_PRINT));
    }
}

// Helper function to clean old sessions (call this periodically)
function cleanOldSessions() {
    global $chatFile, $messagesDir;
    
    $sessions = json_decode(file_get_contents($chatFile), true);
    $cutoffTime = strtotime('-24 hours');
    
    foreach ($sessions as $sessionId => $session) {
        if (strtotime($session['last_activity']) < $cutoffTime) {
            // Remove old session
            unset($sessions[$sessionId]);
            
            // Remove message file
            $messageFile = $messagesDir . $sessionId . '.json';
            if (file_exists($messageFile)) {
                unlink($messageFile);
            }
        }
    }
    
    file_put_contents($chatFile, json_encode($sessions, JSON_PRETTY_PRINT));
}

// Clean old sessions if requested
if (isset($_GET['cleanup'])) {
    cleanOldSessions();
    echo json_encode(['success' => true, 'message' => 'Old sessions cleaned']);
}
?>
