# BadBoyz IPTV Website

A fully functional, advanced IPTV selling website built with modern web technologies. This website is designed for InMotion Hosting deployment and includes all essential features for running a successful IPTV business.

## 🚀 Features

### Frontend Features
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Multi-page Structure**: Complete website with all necessary pages
- **Interactive Elements**: Live chat, FAQ sections, contact forms
- **SEO Optimized**: Meta tags, structured data, and optimized content

### Core Pages
- **Homepage**: Hero section, pricing plans, features showcase
- **Subscription Plans**: Detailed pricing with multiple duration options
- **Free Trial**: 24-hour trial request system
- **Reseller Program**: Complete reseller onboarding and management
- **Installation Tutorials**: Step-by-step guides for all devices
- **Contact & Support**: Multiple contact methods and FAQ
- **Client Area**: Customer dashboard for account management
- **Admin Panel**: Backend management system

### Business Features
- **Payment Integration**: PayPal, Stripe, and cryptocurrency support
- **Customer Management**: User accounts and subscription tracking
- **Reseller System**: Multi-tier reseller program
- **Support System**: Live chat, ticketing, and FAQ
- **Analytics Ready**: Google Analytics and tracking integration

## 📁 File Structure

```
badboyz-iptv/
├── index.html                 # Homepage
├── subscriptions.html         # Subscription plans
├── free-trial.html           # Free trial page
├── reseller.html             # Reseller program
├── tutorials.html            # Installation guides
├── contact.html              # Contact & support
├── about.html                # About us page
├── client-area.html          # Customer dashboard
├── checkout.html             # Payment processing
├── admin/
│   └── index.html            # Admin panel
├── assets/
│   ├── css/
│   │   └── style.css         # Main stylesheet
│   ├── js/
│   │   ├── main.js           # Core JavaScript
│   │   └── checkout.js       # Payment processing
│   └── images/               # Image assets
└── README.md                 # This file
```

## 🛠️ Installation

### For InMotion Hosting

1. **Upload Files**:
   - Upload all files to your public_html directory
   - Ensure proper file permissions (644 for files, 755 for directories)

2. **Configure Domain**:
   - Point your domain to the uploaded files
   - Set up SSL certificate for secure payments

3. **Payment Setup**:
   - Replace PayPal client ID in checkout.html
   - Add your Stripe publishable key in checkout.js
   - Configure webhook endpoints for payment processing

4. **Email Configuration**:
   - Set up SMTP for contact forms and notifications
   - Configure automated email templates

### Local Development

1. **Clone/Download**:
   ```bash
   git clone [repository-url]
   cd streampro-iptv
   ```

2. **Serve Files**:
   - Use a local web server (Apache, Nginx, or Python's built-in server)
   - Or use VS Code Live Server extension

3. **Configure**:
   - Update API keys and endpoints
   - Modify branding and content as needed

## ⚙️ Configuration

### Payment Integration

1. **PayPal**:
   - Get client ID from PayPal Developer Console
   - Replace `YOUR_PAYPAL_CLIENT_ID` in checkout.html

2. **Stripe**:
   - Get publishable key from Stripe Dashboard
   - Replace `pk_test_YOUR_STRIPE_PUBLISHABLE_KEY` in checkout.js

3. **Cryptocurrency**:
   - Integrate with services like CoinGate or BitPay
   - Update crypto payment handlers in checkout.js

### Email Configuration

1. **SMTP Settings**:
   - Configure your hosting provider's SMTP
   - Update contact form handlers

2. **Templates**:
   - Customize email templates for:
     - Welcome emails
     - Trial activations
     - Payment confirmations
     - Support tickets

### Branding Customization

1. **Logo & Images**:
   - Replace logo.png with your brand logo
   - Update hero images and backgrounds
   - Add your company images

2. **Colors & Styling**:
   - Modify CSS variables in style.css
   - Update brand colors throughout the site

3. **Content**:
   - Update company information
   - Modify pricing and plans
   - Customize feature descriptions

## 🔧 Backend Integration

### Database Setup
```sql
-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE,
    password VARCHAR(255),
    plan VARCHAR(50),
    status ENUM('active', 'expired', 'suspended'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    plan_type VARCHAR(50),
    amount DECIMAL(10,2),
    payment_method VARCHAR(50),
    status VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### API Endpoints
- `POST /api/register` - User registration
- `POST /api/login` - User authentication
- `POST /api/trial` - Free trial activation
- `POST /api/payment` - Payment processing
- `GET /api/user/profile` - User profile data
- `PUT /api/user/profile` - Update profile

## 📱 Mobile Optimization

- Fully responsive design
- Touch-friendly interface
- Optimized images and assets
- Fast loading times
- Mobile-specific features

## 🔒 Security Features

- SSL/HTTPS enforcement
- Input validation and sanitization
- CSRF protection
- Secure payment processing
- Password hashing
- Session management

## 📊 Analytics & Tracking

- Google Analytics integration
- Conversion tracking
- User behavior analysis
- Payment funnel tracking
- Customer lifetime value

## 🎨 Customization Guide

### Changing Colors
```css
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
}
```

### Adding New Plans
1. Update pricing cards in subscriptions.html
2. Add plan logic in checkout.js
3. Update database schema if needed

### Custom Features
- Add new pages following the existing structure
- Extend JavaScript functionality in main.js
- Integrate additional payment methods

## 🚀 Deployment Checklist

- [ ] Upload all files to hosting
- [ ] Configure SSL certificate
- [ ] Set up payment gateways
- [ ] Configure email system
- [ ] Test all forms and functionality
- [ ] Set up analytics tracking
- [ ] Configure backup system
- [ ] Test mobile responsiveness
- [ ] Verify SEO optimization
- [ ] Set up monitoring

## 📞 Support

For technical support or customization services:
- Email: <EMAIL>
- Documentation: Available in code comments
- Updates: Check for updates regularly

## 📄 License

This project is licensed under the MIT License. See LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Note**: This is a complete, production-ready IPTV website template. Ensure you comply with all local laws and regulations regarding IPTV services in your jurisdiction.
