/* BadBoyz IPTV - Minimal CSS Test File */

/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    color: #fff;
    line-height: 1.6;
}

/* Test if this CSS loads */
.hero-section {
    display: block !important;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    padding: 4rem 0;
    text-align: center;
}

.hero-section h2 {
    color: white;
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Header Styles */
.header {
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.navbar {
    padding: 1rem 0;
}

.navbar-brand .logo {
    height: 40px;
    width: auto;
}

.navbar-nav .nav-link {
    color: #ccc;
    font-weight: 500;
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: #007bff;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
    color: white;
}

/* Hero Section */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #007bff 0%, #00d4ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #ccc;
    margin-bottom: 2rem;
}

/* Pricing Cards */
.pricing-card {
    background: rgba(255,255,255,0.05);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,123,255,0.2);
}

.pricing-card.popular {
    border: 2px solid #007bff;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 0.5rem 2rem;
    border-radius: 0 0 15px 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

.pricing-header h3 {
    color: #fff;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 1rem;
}

.currency {
    font-size: 1.5rem;
    color: #007bff;
}

.amount {
    font-size: 3rem;
    font-weight: 800;
    color: #007bff;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.pricing-features li {
    padding: 0.5rem 0;
    color: #ccc;
    display: flex;
    align-items: center;
}

.pricing-features i {
    color: #28a745;
    margin-right: 0.75rem;
    width: 16px;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    color: #ccc;
    text-align: center;
    padding: 2rem 0;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .pricing-card.popular {
        transform: none;
    }
}
