# BadBoyz IPTV - InMotion Hosting Deployment Guide

## 🚀 **Complete Step-by-Step Deployment Instructions**

### **Prerequisites:**
- InMotion Hosting account (Shared, VPS, or Dedicated)
- Domain name (registered or transferred to InMotion)
- FTP client (FileZilla recommended) or use cPanel File Manager
- All website files ready for upload

---

## **Step 1: Access Your InMotion Hosting cPanel**

### **Method 1: Direct cPanel Access**
1. Go to `https://your-domain.com/cpanel`
2. Enter your cPanel username and password
3. Click "Log in"

### **Method 2: Through InMotion AMP**
1. Log into your InMotion account at `https://my.inmotionhosting.com`
2. Click on "cPanel" next to your hosting account
3. This will automatically log you into cPanel

---

## **Step 2: Prepare Your Domain**

### **If Domain is Already Pointed to InMotion:**
- Skip to Step 3

### **If You Need to Point Your Domain:**
1. **In cPanel**, go to "Subdomains" or "Addon Domains"
2. **Add your domain** if it's not the primary domain
3. **Update nameservers** at your domain registrar to:
   - `ns1.inmotionhosting.com`
   - `ns2.inmotionhosting.com`
4. **Wait 24-48 hours** for DNS propagation

---

## **Step 3: Upload Website Files**

### **Option A: Using cPanel File Manager (Recommended for Beginners)**

1. **Open File Manager:**
   - In cPanel, click "File Manager"
   - Navigate to `public_html` folder
   - If using a subdomain, go to `public_html/subdomain-name`

2. **Clear Default Files:**
   - Delete any existing files (like `index.html`, `cgi-bin` if empty)
   - Keep `.htaccess` if it exists

3. **Upload Website Files:**
   - Click "Upload" button
   - Select all your website files or upload as ZIP
   - If uploading ZIP, right-click and "Extract" after upload

4. **Set Permissions:**
   - Select all files and folders
   - Click "Permissions"
   - Set folders to `755` and files to `644`

### **Option B: Using FTP (FileZilla)**

1. **Download FileZilla:** `https://filezilla-project.org/`

2. **Get FTP Credentials:**
   - In cPanel, go to "FTP Accounts"
   - Use your main cPanel credentials or create new FTP account

3. **Connect to Server:**
   - **Host:** `ftp.your-domain.com` or your server IP
   - **Username:** Your cPanel username
   - **Password:** Your cPanel password
   - **Port:** 21

4. **Upload Files:**
   - Navigate to `public_html` on remote side
   - Upload all website files from local computer
   - Ensure file permissions are correct

---

## **Step 4: Configure SSL Certificate**

### **Enable Free SSL (Let's Encrypt):**
1. In cPanel, go to "SSL/TLS"
2. Click "Let's Encrypt SSL"
3. Select your domain
4. Click "Issue" to generate certificate
5. Wait 5-10 minutes for activation

### **Force HTTPS Redirect:**
1. In cPanel, go to "SSL/TLS"
2. Click "Force HTTPS Redirect"
3. Enable for your domain

---

## **Step 5: Set Up Email Accounts**

### **Create Email Accounts:**
1. In cPanel, go to "Email Accounts"
2. Create these accounts:
   - `<EMAIL>`
   - `<EMAIL>`
   - `<EMAIL>`

### **Configure Email in Website:**
1. Edit `api/contact.php`
2. Update SMTP settings:
```php
$config = [
    'smtp_host' => 'mail.your-domain.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'your-email-password',
    'admin_email' => '<EMAIL>'
];
```

---

## **Step 6: Database Setup (Optional)**

### **Create MySQL Database:**
1. In cPanel, go to "MySQL Databases"
2. Create database: `badboyz_iptv`
3. Create user with full privileges
4. Note down database credentials

### **Import Database Schema:**
1. Go to "phpMyAdmin" in cPanel
2. Select your database
3. Import the SQL schema (if you have one)

---

## **Step 7: Configure Payment Gateways**

### **PayPal Setup:**
1. Get PayPal Client ID from PayPal Developer Console
2. Edit `checkout.html`:
```html
<script src="https://www.paypal.com/sdk/js?client-id=YOUR_ACTUAL_PAYPAL_CLIENT_ID&currency=USD"></script>
```

### **Stripe Setup (Optional):**
1. Get Stripe keys from Stripe Dashboard
2. Edit `assets/js/checkout.js`:
```javascript
const stripe = Stripe('pk_live_YOUR_ACTUAL_STRIPE_KEY');
```

---

## **Step 8: Update Configuration**

### **Edit config.js:**
```javascript
site: {
    name: "BadBoyz IPTV",
    url: "https://your-actual-domain.com",
    // ... other settings
},
contact: {
    email: "<EMAIL>",
    // ... other contact info
}
```

### **Update All HTML Files:**
- Replace any remaining "StreamPro" references with "BadBoyz"
- Update contact information
- Verify all links work correctly

---

## **Step 9: Test Everything**

### **Website Functionality:**
- [ ] All pages load correctly
- [ ] Navigation works
- [ ] Forms submit properly
- [ ] Live chat functions
- [ ] Mobile responsiveness

### **Payment Testing:**
- [ ] PayPal sandbox mode works
- [ ] Stripe test mode works
- [ ] Order process completes

### **Email Testing:**
- [ ] Contact form sends emails
- [ ] Trial requests work
- [ ] Email notifications arrive

---

## **Step 10: Go Live Checklist**

### **Final Steps:**
- [ ] Switch payment gateways to live mode
- [ ] Update Google Analytics tracking code
- [ ] Submit sitemap to Google Search Console
- [ ] Set up website monitoring
- [ ] Create regular backups
- [ ] Test from different devices/browsers

---

## **Step 11: Post-Launch Optimization**

### **Performance:**
1. **Enable Gzip Compression:**
   - Already included in `.htaccess`

2. **Optimize Images:**
   - Compress images before upload
   - Use WebP format when possible

3. **Enable Caching:**
   - InMotion often has caching enabled by default
   - Check "Optimize Website" in cPanel

### **Security:**
1. **Regular Updates:**
   - Keep website files updated
   - Monitor for security issues

2. **Backup Schedule:**
   - InMotion provides automatic backups
   - Download manual backups monthly

### **SEO:**
1. **Google Analytics:**
   - Add tracking code to all pages
   - Set up conversion goals

2. **Google Search Console:**
   - Verify domain ownership
   - Submit sitemap.xml

---

## **Troubleshooting Common Issues**

### **Website Not Loading:**
- Check DNS propagation: `https://dnschecker.org`
- Verify files are in correct directory (`public_html`)
- Check file permissions (755 for folders, 644 for files)

### **SSL Certificate Issues:**
- Wait 24 hours after domain setup
- Try regenerating Let's Encrypt certificate
- Contact InMotion support if issues persist

### **Email Not Working:**
- Verify email accounts exist in cPanel
- Check spam folders
- Test with simple PHP mail script

### **Payment Issues:**
- Verify API keys are correct
- Check sandbox vs live mode settings
- Test with small amounts first

---

## **InMotion Hosting Support**

### **Contact Methods:**
- **Phone:** Available 24/7
- **Live Chat:** Through customer portal
- **Email:** Support tickets
- **Knowledge Base:** Extensive documentation

### **What InMotion Can Help With:**
- Server configuration issues
- SSL certificate problems
- Email setup assistance
- DNS and domain issues
- Performance optimization

---

## **File Structure on Server**

```
public_html/
├── index.html
├── subscriptions.html
├── free-trial.html
├── reseller.html
├── tutorials.html
├── contact.html
├── about.html
├── client-area.html
├── checkout.html
├── admin/
│   └── index.html
├── assets/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   ├── main.js
│   │   ├── checkout.js
│   │   └── livechat.js
│   └── images/
├── api/
│   └── contact.php
├── config.js
├── .htaccess
└── README.md
```

---

## **Success! Your BadBoyz IPTV Website is Live! 🎉**

Your professional IPTV website is now deployed and ready to:
- Accept customer subscriptions
- Process payments securely
- Provide 24/7 live chat support
- Generate leads and sales

**Next Steps:**
1. Start marketing your IPTV service
2. Monitor website analytics
3. Provide excellent customer support
4. Scale your business as it grows

**Need Help?** Contact InMotion Hosting support - they're available 24/7 to assist with any technical issues.
