<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - StreamPro IPTV</title>
    <meta name="description" content="StreamPro IPTV Admin Panel - Manage customers, subscriptions, resellers, and system settings.">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            padding: 2rem 0;
        }
        
        .admin-content {
            margin-left: 250px;
            padding: 2rem;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
            min-height: 100vh;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 2rem;
        }
        
        .sidebar-brand h3 {
            color: #fff;
            margin: 0;
        }
        
        .sidebar-nav {
            list-style: none;
            padding: 0;
        }
        
        .sidebar-nav li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-nav a {
            display: block;
            padding: 1rem 1.5rem;
            color: #ccc;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: rgba(0,123,255,0.2);
            color: #007bff;
        }
        
        .sidebar-nav i {
            margin-right: 0.75rem;
            width: 20px;
        }
        
        .admin-header {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .admin-header h1 {
            color: #fff;
            margin: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 2.5rem;
            color: #007bff;
            margin-bottom: 1rem;
        }
        
        .stat-card h3 {
            color: #fff;
            margin-bottom: 0.5rem;
        }
        
        .stat-card p {
            color: #ccc;
            margin: 0;
        }
        
        .admin-table {
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .admin-table h3 {
            color: #fff;
            margin-bottom: 1.5rem;
        }
        
        .table-dark {
            background: transparent;
        }
        
        .table-dark th,
        .table-dark td {
            border-color: rgba(255,255,255,0.1);
            color: #ccc;
        }
        
        .table-dark th {
            color: #fff;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .btn-sm {
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .admin-sidebar.show {
                transform: translateX(0);
            }
            
            .admin-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Sidebar -->
    <div class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-brand">
            <h3>StreamPro Admin</h3>
        </div>
        
        <ul class="sidebar-nav">
            <li><a href="#dashboard" class="nav-link active" data-section="dashboard">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a></li>
            <li><a href="#customers" class="nav-link" data-section="customers">
                <i class="fas fa-users"></i> Customers
            </a></li>
            <li><a href="#subscriptions" class="nav-link" data-section="subscriptions">
                <i class="fas fa-credit-card"></i> Subscriptions
            </a></li>
            <li><a href="#resellers" class="nav-link" data-section="resellers">
                <i class="fas fa-handshake"></i> Resellers
            </a></li>
            <li><a href="#servers" class="nav-link" data-section="servers">
                <i class="fas fa-server"></i> Servers
            </a></li>
            <li><a href="#channels" class="nav-link" data-section="channels">
                <i class="fas fa-tv"></i> Channels
            </a></li>
            <li><a href="#reports" class="nav-link" data-section="reports">
                <i class="fas fa-chart-bar"></i> Reports
            </a></li>
            <li><a href="#settings" class="nav-link" data-section="settings">
                <i class="fas fa-cog"></i> Settings
            </a></li>
            <li><a href="../index.html" class="nav-link">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a></li>
        </ul>
    </div>

    <!-- Admin Content -->
    <div class="admin-content">
        <!-- Mobile Menu Toggle -->
        <button class="btn btn-primary d-md-none mb-3" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i> Menu
        </button>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="admin-section">
            <div class="admin-header">
                <h1>Dashboard Overview</h1>
                <p>Welcome to StreamPro IPTV Admin Panel</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-users"></i>
                    <h3 id="totalCustomers">1,247</h3>
                    <p>Total Customers</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-credit-card"></i>
                    <h3 id="activeSubscriptions">1,089</h3>
                    <p>Active Subscriptions</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-handshake"></i>
                    <h3 id="totalResellers">45</h3>
                    <p>Active Resellers</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-dollar-sign"></i>
                    <h3 id="monthlyRevenue">$18,450</h3>
                    <p>Monthly Revenue</p>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="admin-table">
                <h3>Recent Activity</h3>
                <div class="table-responsive">
                    <table class="table table-dark">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Action</th>
                                <th>User</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2 min ago</td>
                                <td><span class="badge bg-success">New Subscription</span></td>
                                <td><EMAIL></td>
                                <td>12 Month Plan - $85</td>
                            </tr>
                            <tr>
                                <td>15 min ago</td>
                                <td><span class="badge bg-info">Trial Request</span></td>
                                <td><EMAIL></td>
                                <td>24-hour trial activated</td>
                            </tr>
                            <tr>
                                <td>1 hour ago</td>
                                <td><span class="badge bg-warning">Reseller Application</span></td>
                                <td><EMAIL></td>
                                <td>Professional Plan</td>
                            </tr>
                            <tr>
                                <td>2 hours ago</td>
                                <td><span class="badge bg-primary">Payment</span></td>
                                <td><EMAIL></td>
                                <td>6 Month Plan - $55</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Customers Section -->
        <div id="customers-section" class="admin-section" style="display: none;">
            <div class="admin-header">
                <h1>Customer Management</h1>
                <button class="btn btn-primary">Add New Customer</button>
            </div>

            <div class="admin-table">
                <h3>All Customers</h3>
                <div class="table-responsive">
                    <table class="table table-dark">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Email</th>
                                <th>Plan</th>
                                <th>Status</th>
                                <th>Expiry</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#1001</td>
                                <td><EMAIL></td>
                                <td>12 Month</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>Dec 15, 2025</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">Edit</button>
                                    <button class="btn btn-sm btn-outline-danger">Suspend</button>
                                </td>
                            </tr>
                            <tr>
                                <td>#1002</td>
                                <td><EMAIL></td>
                                <td>6 Month</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>Aug 20, 2025</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">Edit</button>
                                    <button class="btn btn-sm btn-outline-danger">Suspend</button>
                                </td>
                            </tr>
                            <tr>
                                <td>#1003</td>
                                <td><EMAIL></td>
                                <td>3 Month</td>
                                <td><span class="badge bg-warning">Expired</span></td>
                                <td>Jan 10, 2025</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">Edit</button>
                                    <button class="btn btn-sm btn-outline-success">Renew</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Other sections would be added here -->
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Admin Panel Functions
        function toggleSidebar() {
            document.getElementById('adminSidebar').classList.toggle('show');
        }

        // Navigation
        document.querySelectorAll('.nav-link[data-section]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Update active nav
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                // Show section
                const section = this.dataset.section;
                document.querySelectorAll('.admin-section').forEach(s => s.style.display = 'none');
                document.getElementById(section + '-section').style.display = 'block';
            });
        });

        // Simulate real-time updates
        setInterval(() => {
            const customers = document.getElementById('totalCustomers');
            const current = parseInt(customers.textContent.replace(',', ''));
            customers.textContent = (current + Math.floor(Math.random() * 3)).toLocaleString();
        }, 30000);
    </script>
</body>
</html>
