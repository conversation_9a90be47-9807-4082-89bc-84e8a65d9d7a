<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Chat Admin - BadBoyz IPTV</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        .chat-admin-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .chat-sessions {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .chat-session {
            border-bottom: 1px solid #eee;
            padding: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .chat-session:hover {
            background: #f8f9fa;
        }
        
        .chat-session.active {
            background: #e3f2fd;
            border-left: 4px solid #007bff;
        }
        
        .chat-session.unread {
            background: #fff3cd;
        }
        
        .session-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .visitor-info h6 {
            margin: 0;
            color: #333;
        }
        
        .visitor-info small {
            color: #666;
        }
        
        .session-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .chat-window {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 1rem;
            border-radius: 15px 15px 0 0;
        }
        
        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }
        
        .message.customer {
            justify-content: flex-start;
        }
        
        .message.admin {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 15px;
            position: relative;
        }
        
        .message.customer .message-content {
            background: #e9ecef;
            color: #333;
        }
        
        .message.admin .message-content {
            background: #007bff;
            color: white;
        }
        
        .message-time {
            font-size: 0.75rem;
            opacity: 0.7;
            margin-top: 0.25rem;
        }
        
        .chat-input {
            padding: 1rem;
            border-top: 1px solid #eee;
            background: white;
            border-radius: 0 0 15px 15px;
        }
        
        .input-group {
            display: flex;
            gap: 0.5rem;
        }
        
        .chat-input input {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 25px;
            padding: 0.75rem 1rem;
            outline: none;
        }
        
        .send-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .send-btn:hover {
            background: #0056b3;
        }
        
        .quick-replies {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .quick-reply {
            background: #e9ecef;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .quick-reply:hover {
            background: #007bff;
            color: white;
        }
        
        .no-session {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="chat-admin-container">
        <div class="row mb-4">
            <div class="col-12">
                <h1><i class="fas fa-comments"></i> Live Chat Admin</h1>
                <p class="text-muted">Manage customer conversations and provide real-time support</p>
            </div>
        </div>
        
        <div class="row">
            <!-- Chat Sessions List -->
            <div class="col-lg-4 mb-4">
                <div class="chat-sessions">
                    <div class="p-3 border-bottom">
                        <h5 class="mb-0">Active Chats</h5>
                        <small class="text-muted">3 active conversations</small>
                    </div>
                    
                    <div class="chat-session active unread" data-session="1">
                        <div class="session-info">
                            <div class="visitor-info">
                                <h6>Visitor #1234</h6>
                                <small>Last message: 2 minutes ago</small>
                            </div>
                            <div class="session-status">
                                <span class="status-badge status-online">Online</span>
                                <span class="badge bg-danger">2</span>
                            </div>
                        </div>
                        <p class="mb-0 mt-2 text-muted small">I need help with my subscription...</p>
                    </div>
                    
                    <div class="chat-session" data-session="2">
                        <div class="session-info">
                            <div class="visitor-info">
                                <h6>Visitor #1235</h6>
                                <small>Last message: 5 minutes ago</small>
                            </div>
                            <div class="session-status">
                                <span class="status-badge status-online">Online</span>
                            </div>
                        </div>
                        <p class="mb-0 mt-2 text-muted small">How do I set up on my Smart TV?</p>
                    </div>
                    
                    <div class="chat-session" data-session="3">
                        <div class="session-info">
                            <div class="visitor-info">
                                <h6>Visitor #1236</h6>
                                <small>Last message: 15 minutes ago</small>
                            </div>
                            <div class="session-status">
                                <span class="status-badge status-offline">Offline</span>
                            </div>
                        </div>
                        <p class="mb-0 mt-2 text-muted small">Payment not working</p>
                    </div>
                </div>
            </div>
            
            <!-- Chat Window -->
            <div class="col-lg-8">
                <div class="chat-window">
                    <div class="chat-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-0">Visitor #1234</h5>
                                <small>Online • Viewing: Subscriptions Page</small>
                            </div>
                            <div>
                                <button class="btn btn-light btn-sm me-2">
                                    <i class="fas fa-user"></i> Profile
                                </button>
                                <button class="btn btn-light btn-sm">
                                    <i class="fas fa-times"></i> End Chat
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chat-messages" id="chatMessages">
                        <div class="message customer">
                            <div class="message-content">
                                <p class="mb-0">Hi, I'm interested in your IPTV service. Can you help me choose the right plan?</p>
                                <div class="message-time">2:30 PM</div>
                            </div>
                        </div>
                        
                        <div class="message admin">
                            <div class="message-content">
                                <p class="mb-0">Hello! I'd be happy to help you choose the perfect plan. How many devices do you plan to use simultaneously?</p>
                                <div class="message-time">2:31 PM</div>
                            </div>
                        </div>
                        
                        <div class="message customer">
                            <div class="message-content">
                                <p class="mb-0">I need it for about 3 devices - my TV, tablet, and phone. What would you recommend?</p>
                                <div class="message-time">2:33 PM</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chat-input">
                        <div class="quick-replies">
                            <button class="quick-reply" onclick="insertQuickReply('Thank you for contacting BadBoyz IPTV! How can I help you today?')">Welcome</button>
                            <button class="quick-reply" onclick="insertQuickReply('For 3 devices, I recommend our Pro Plan at $30/month with 3 simultaneous streams.')">Pro Plan</button>
                            <button class="quick-reply" onclick="insertQuickReply('You can try our service with a free 24-hour trial. Would you like me to set that up?')">Free Trial</button>
                            <button class="quick-reply" onclick="insertQuickReply('Let me transfer you to our technical support team for setup assistance.')">Tech Support</button>
                        </div>
                        
                        <div class="input-group">
                            <input type="text" id="messageInput" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
                            <button class="send-btn" onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Chat session management
        document.querySelectorAll('.chat-session').forEach(session => {
            session.addEventListener('click', function() {
                // Remove active class from all sessions
                document.querySelectorAll('.chat-session').forEach(s => s.classList.remove('active'));
                // Add active class to clicked session
                this.classList.add('active');
                // Remove unread badge
                this.classList.remove('unread');
                const badge = this.querySelector('.badge');
                if (badge) badge.remove();
                
                // Load chat messages for this session
                loadChatSession(this.dataset.session);
            });
        });
        
        function loadChatSession(sessionId) {
            // In a real implementation, this would load messages from your backend
            console.log('Loading chat session:', sessionId);
        }
        
        function insertQuickReply(message) {
            document.getElementById('messageInput').value = message;
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                // Add message to chat
                const chatMessages = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message admin';
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <p class="mb-0">${message}</p>
                        <div class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                    </div>
                `;
                chatMessages.appendChild(messageDiv);
                
                // Clear input
                input.value = '';
                
                // Scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                // In a real implementation, send message to backend
                console.log('Sending message:', message);
            }
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // Auto-scroll to bottom on page load
        document.addEventListener('DOMContentLoaded', function() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        });
    </script>
</body>
</html>
