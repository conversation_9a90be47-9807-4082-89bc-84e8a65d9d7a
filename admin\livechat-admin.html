<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Chat Admin - BadBoyz IPTV</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .chat-admin-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .admin-header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .admin-header h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .admin-stats {
            display: flex;
            gap: 2rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .chat-sessions {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .sessions-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 20px 20px 0 0;
        }

        .sessions-header h5 {
            margin: 0;
            font-weight: 600;
        }

        .sessions-header small {
            opacity: 0.9;
        }

        .chat-session {
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .chat-session::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: transparent;
            transition: all 0.3s ease;
        }

        .chat-session:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(5px);
        }

        .chat-session.active {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(10px);
        }

        .chat-session.active::before {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .chat-session.unread {
            background: rgba(255, 193, 7, 0.1);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }
        
        .session-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .visitor-info h6 {
            margin: 0;
            color: #333;
        }
        
        .visitor-info small {
            color: #666;
        }
        
        .session-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .chat-window {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            height: 700px;
            display: flex;
            flex-direction: column;
            border: 1px solid rgba(255,255,255,0.2);
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 20px 20px 0 0;
            position: relative;
            overflow: hidden;
        }

        .chat-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }
        
        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }
        
        .message.customer {
            justify-content: flex-start;
        }
        
        .message.admin {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 15px;
            position: relative;
        }
        
        .message.customer .message-content {
            background: #e9ecef;
            color: #333;
        }
        
        .message.admin .message-content {
            background: #007bff;
            color: white;
        }
        
        .message-time {
            font-size: 0.75rem;
            opacity: 0.7;
            margin-top: 0.25rem;
        }
        
        .chat-input {
            padding: 1rem;
            border-top: 1px solid #eee;
            background: white;
            border-radius: 0 0 15px 15px;
        }
        
        .input-group {
            display: flex;
            gap: 0.5rem;
        }
        
        .chat-input input {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 25px;
            padding: 0.75rem 1rem;
            outline: none;
        }
        
        .send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .send-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .send-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
        }

        .send-btn:active {
            transform: scale(0.95);
        }
        
        .quick-replies {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .quick-reply {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .quick-reply::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: left 0.3s ease;
            z-index: 1;
        }

        .quick-reply:hover::before {
            left: 0;
        }

        .quick-reply:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .quick-reply span {
            position: relative;
            z-index: 2;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #666;
            font-style: italic;
            padding: 1rem;
        }

        .typing-dots {
            display: flex;
            gap: 0.25rem;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #667eea;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }
        
        .no-session {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="chat-admin-container">
        <!-- Enhanced Admin Header -->
        <div class="admin-header">
            <h1><i class="fas fa-comments-dollar"></i> BadBoyz IPTV Live Chat Admin</h1>
            <p class="text-muted mb-0">Manage customer conversations and provide real-time support</p>

            <div class="admin-stats">
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Active Chats</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">Today's Chats</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2.3m</div>
                    <div class="stat-label">Avg Response</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">Satisfaction</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Chat Sessions List -->
            <div class="col-lg-4 mb-4">
                <div class="chat-sessions">
                    <div class="sessions-header">
                        <h5 class="mb-0"><i class="fas fa-users"></i> Active Chats</h5>
                        <small>3 active conversations • 2 unread</small>
                    </div>
                    
                    <div class="chat-session active unread" data-session="1">
                        <div class="session-info">
                            <div class="visitor-info">
                                <h6>Visitor #1234</h6>
                                <small>Last message: 2 minutes ago</small>
                            </div>
                            <div class="session-status">
                                <span class="status-badge status-online">Online</span>
                                <span class="badge bg-danger">2</span>
                            </div>
                        </div>
                        <p class="mb-0 mt-2 text-muted small">I need help with my subscription...</p>
                    </div>
                    
                    <div class="chat-session" data-session="2">
                        <div class="session-info">
                            <div class="visitor-info">
                                <h6>Visitor #1235</h6>
                                <small>Last message: 5 minutes ago</small>
                            </div>
                            <div class="session-status">
                                <span class="status-badge status-online">Online</span>
                            </div>
                        </div>
                        <p class="mb-0 mt-2 text-muted small">How do I set up on my Smart TV?</p>
                    </div>
                    
                    <div class="chat-session" data-session="3">
                        <div class="session-info">
                            <div class="visitor-info">
                                <h6>Visitor #1236</h6>
                                <small>Last message: 15 minutes ago</small>
                            </div>
                            <div class="session-status">
                                <span class="status-badge status-offline">Offline</span>
                            </div>
                        </div>
                        <p class="mb-0 mt-2 text-muted small">Payment not working</p>
                    </div>
                </div>
            </div>
            
            <!-- Chat Window -->
            <div class="col-lg-8">
                <div class="chat-window">
                    <div class="chat-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-0">Visitor #1234</h5>
                                <small>Online • Viewing: Subscriptions Page</small>
                            </div>
                            <div>
                                <button class="btn btn-light btn-sm me-2">
                                    <i class="fas fa-user"></i> Profile
                                </button>
                                <button class="btn btn-light btn-sm">
                                    <i class="fas fa-times"></i> End Chat
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chat-messages" id="chatMessages">
                        <div class="message customer">
                            <div class="message-content">
                                <p class="mb-0">Hi, I'm interested in your IPTV service. Can you help me choose the right plan?</p>
                                <div class="message-time">2:30 PM</div>
                            </div>
                        </div>
                        
                        <div class="message admin">
                            <div class="message-content">
                                <p class="mb-0">Hello! I'd be happy to help you choose the perfect plan. How many devices do you plan to use simultaneously?</p>
                                <div class="message-time">2:31 PM</div>
                            </div>
                        </div>

                        <div class="message customer">
                            <div class="message-content">
                                <p class="mb-0">I need it for about 3 devices - my TV, tablet, and phone. What would you recommend?</p>
                                <div class="message-time">2:33 PM</div>
                            </div>
                        </div>

                        <!-- Typing Indicator (hidden by default) -->
                        <div class="typing-indicator" id="typingIndicator" style="display: none;">
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                            <span>Customer is typing...</span>
                        </div>
                    </div>
                    
                    <div class="chat-input">
                        <div class="quick-replies">
                            <button class="quick-reply" onclick="insertQuickReply('👋 Thank you for contacting BadBoyz IPTV! How can I help you today?')">
                                <span><i class="fas fa-hand-wave"></i> Welcome</span>
                            </button>
                            <button class="quick-reply" onclick="insertQuickReply('💎 For 3 devices, I recommend our Pro Plan at $30/month with 3 simultaneous streams.')">
                                <span><i class="fas fa-star"></i> Pro Plan</span>
                            </button>
                            <button class="quick-reply" onclick="insertQuickReply('🎁 You can try our service with a free 24-hour trial. Would you like me to set that up?')">
                                <span><i class="fas fa-gift"></i> Free Trial</span>
                            </button>
                            <button class="quick-reply" onclick="insertQuickReply('🔧 Let me transfer you to our technical support team for setup assistance.')">
                                <span><i class="fas fa-tools"></i> Tech Support</span>
                            </button>
                            <button class="quick-reply" onclick="insertQuickReply('📱 You can download ImPlayer from the App Store or Google Play for the best IPTV experience.')">
                                <span><i class="fas fa-mobile-alt"></i> ImPlayer</span>
                            </button>
                            <button class="quick-reply" onclick="insertQuickReply('💳 We accept PayPal, Stripe, and cryptocurrency payments for your convenience.')">
                                <span><i class="fas fa-credit-card"></i> Payment</span>
                            </button>
                        </div>
                        
                        <div class="input-group">
                            <input type="text" id="messageInput" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
                            <button class="send-btn" onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Chat session management
        document.querySelectorAll('.chat-session').forEach(session => {
            session.addEventListener('click', function() {
                // Remove active class from all sessions
                document.querySelectorAll('.chat-session').forEach(s => s.classList.remove('active'));
                // Add active class to clicked session
                this.classList.add('active');
                // Remove unread badge
                this.classList.remove('unread');
                const badge = this.querySelector('.badge');
                if (badge) badge.remove();
                
                // Load chat messages for this session
                loadChatSession(this.dataset.session);
            });
        });
        
        function loadChatSession(sessionId) {
            // In a real implementation, this would load messages from your backend
            console.log('Loading chat session:', sessionId);
        }
        
        function insertQuickReply(message) {
            document.getElementById('messageInput').value = message;
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (message) {
                // Show typing indicator briefly
                showTypingIndicator();

                setTimeout(() => {
                    hideTypingIndicator();

                    // Add message to chat with animation
                    const chatMessages = document.getElementById('chatMessages');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message admin';
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transform = 'translateY(20px)';
                    messageDiv.innerHTML = `
                        <div class="message-content">
                            <p class="mb-0">${message}</p>
                            <div class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                        </div>
                    `;
                    chatMessages.appendChild(messageDiv);

                    // Animate message appearance
                    setTimeout(() => {
                        messageDiv.style.transition = 'all 0.3s ease';
                        messageDiv.style.opacity = '1';
                        messageDiv.style.transform = 'translateY(0)';
                    }, 10);

                    // Clear input
                    input.value = '';

                    // Scroll to bottom smoothly
                    chatMessages.scrollTo({
                        top: chatMessages.scrollHeight,
                        behavior: 'smooth'
                    });

                    // Play send sound (optional)
                    playNotificationSound();

                    // In a real implementation, send message to backend
                    console.log('Sending message:', message);
                }, 500);
            }
        }

        function showTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            if (indicator) {
                indicator.style.display = 'flex';
                const chatMessages = document.getElementById('chatMessages');
                chatMessages.scrollTo({
                    top: chatMessages.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            if (indicator) {
                indicator.style.display = 'none';
            }
        }

        function playNotificationSound() {
            // Create a subtle notification sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // Auto-scroll to bottom on page load
        document.addEventListener('DOMContentLoaded', function() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        });
    </script>
</body>
</html>
