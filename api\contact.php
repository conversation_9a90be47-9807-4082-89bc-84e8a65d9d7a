<?php
/**
 * StreamPro IPTV - Contact Form Handler
 * Handles contact form submissions and sends emails
 */

// Enable CORS for API requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration
$config = [
    'smtp_host' => 'smtp.badboyzmedia.org',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'szQRJ%?f.(4n',
    'admin_email' => '<EMAIL>',
    'site_name' => 'badboyz media'
];

/**
 * Validate email address
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

/**
 * Send email using PHP mail function
 */
function sendEmail($to, $subject, $message, $headers) {
    return mail($to, $subject, $message, $headers);
}

/**
 * Generate email template
 */
function generateEmailTemplate($data, $type = 'contact') {
    $templates = [
        'contact' => "
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background: #f9f9f9; }
                    .footer { padding: 20px; text-align: center; color: #666; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>New Contact Form Submission</h2>
                    </div>
                    <div class='content'>
                        <p><strong>Name:</strong> {$data['firstName']} {$data['lastName']}</p>
                        <p><strong>Email:</strong> {$data['email']}</p>
                        <p><strong>Phone:</strong> {$data['phone']}</p>
                        <p><strong>Subject:</strong> {$data['subject']}</p>
                        <p><strong>Priority:</strong> {$data['priority']}</p>
                        <p><strong>Message:</strong></p>
                        <p>{$data['message']}</p>
                    </div>
                    <div class='footer'>
                        <p>This message was sent from StreamPro IPTV contact form.</p>
                    </div>
                </div>
            </body>
            </html>
        ",
        'trial' => "
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #28a745; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background: #f9f9f9; }
                    .credentials { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0; }
                    .footer { padding: 20px; text-align: center; color: #666; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h2>Your Free Trial is Ready!</h2>
                    </div>
                    <div class='content'>
                        <p>Hello {$data['firstName']},</p>
                        <p>Thank you for requesting a free trial. Your 24-hour trial account is now active!</p>
                        
                        <div class='credentials'>
                            <h3>Your Trial Credentials:</h3>
                            <p><strong>Username:</strong> trial_" . uniqid() . "</p>
                            <p><strong>Password:</strong> " . substr(md5(time()), 0, 8) . "</p>
                            <p><strong>Server URL:</strong> http://server.streampro-iptv.com:8080</p>
                        </div>
                        
                        <p>Your trial will expire in 24 hours. If you enjoy our service, you can subscribe at any time.</p>
                        <p>Need help setting up? Check our installation tutorials or contact support.</p>
                    </div>
                    <div class='footer'>
                        <p>Thank you for choosing StreamPro IPTV!</p>
                    </div>
                </div>
            </body>
            </html>
        "
    ];
    
    return $templates[$type] ?? $templates['contact'];
}

// Main request handler
try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        // Fallback to form data
        $input = $_POST;
    }

    // Determine request type
    $requestType = $input['type'] ?? 'contact';
    
    // Validate required fields based on type
    $requiredFields = [
        'contact' => ['firstName', 'lastName', 'email', 'subject', 'message'],
        'trial' => ['firstName', 'lastName', 'email', 'country', 'device'],
        'reseller' => ['firstName', 'lastName', 'email', 'phone', 'country', 'businessType']
    ];

    $required = $requiredFields[$requestType] ?? $requiredFields['contact'];
    
    foreach ($required as $field) {
        if (empty($input[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }

    // Validate email
    if (!validateEmail($input['email'])) {
        throw new Exception('Invalid email address');
    }

    // Sanitize all input data
    $data = [];
    foreach ($input as $key => $value) {
        $data[$key] = sanitizeInput($value);
    }

    // Process based on request type
    switch ($requestType) {
        case 'contact':
            $subject = "New Contact Form Submission - " . $data['subject'];
            $message = generateEmailTemplate($data, 'contact');
            $to = $config['admin_email'];
            break;
            
        case 'trial':
            $subject = "Free Trial Request - " . $data['firstName'] . " " . $data['lastName'];
            $message = generateEmailTemplate($data, 'trial');
            $to = $data['email'];
            
            // Also send notification to admin
            $adminMessage = "New trial request from: " . $data['email'];
            sendEmail($config['admin_email'], "New Trial Request", $adminMessage, "From: " . $config['smtp_username']);
            break;
            
        case 'reseller':
            $subject = "New Reseller Application - " . $data['firstName'] . " " . $data['lastName'];
            $message = generateEmailTemplate($data, 'contact');
            $to = $config['admin_email'];
            break;
            
        default:
            throw new Exception('Invalid request type');
    }

    // Email headers
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . $config['site_name'] . ' <' . $config['smtp_username'] . '>',
        'Reply-To: ' . $data['email'],
        'X-Mailer: PHP/' . phpversion()
    ];

    // Send email
    $emailSent = sendEmail($to, $subject, $message, implode("\r\n", $headers));

    if (!$emailSent) {
        throw new Exception('Failed to send email');
    }

    // Log the submission (optional)
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $requestType,
        'email' => $data['email'],
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    file_put_contents('logs/submissions.log', json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);

    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Your request has been submitted successfully!',
        'type' => $requestType
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Rate limiting (simple implementation)
function checkRateLimit($ip, $limit = 5, $window = 3600) {
    $file = "logs/rate_limit_$ip.txt";
    $now = time();
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        $requests = array_filter($data['requests'], function($time) use ($now, $window) {
            return ($now - $time) < $window;
        });
        
        if (count($requests) >= $limit) {
            return false;
        }
        
        $requests[] = $now;
    } else {
        $requests = [$now];
    }
    
    file_put_contents($file, json_encode(['requests' => $requests]));
    return true;
}

// Database logging (if you have a database)
function logToDatabase($data, $type) {
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=streampro_iptv', 'username', 'password');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            INSERT INTO form_submissions (type, email, data, ip_address, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $type,
            $data['email'],
            json_encode($data),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
        
        return true;
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}
?>
