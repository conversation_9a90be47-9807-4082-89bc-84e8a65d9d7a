# BadBoyz IPTV - Branding Update Checklist

## ✅ **Completed Updates:**

### **Core Configuration:**
- [x] `config.js` - Updated site name, contact info, social media
- [x] `README.md` - Updated project name and references
- [x] `index.html` - Updated title, meta tags, hero section, navbar
- [x] `about.html` - Updated title, navbar, page title
- [x] `assets/js/livechat.js` - Updated welcome messages

### **Contact Information Updated:**
- [x] Email: `<EMAIL>`
- [x] Phone: `******-BADBOYZ`
- [x] Telegram: `https://t.me/badboyziptv`
- [x] Social Media: Updated all platform URLs

## 📝 **Additional Updates Needed:**

### **Before Going Live, Update These:**

#### **1. Logo and Images:**
- [ ] Replace `assets/images/logo.png` with BadBoyz IPTV logo
- [ ] Update favicon (`assets/images/favicon.ico`)
- [ ] Replace any hero/banner images with branded versions

#### **2. Remaining HTML Pages:**
- [ ] `subscriptions.html` - Update title, navbar, content
- [ ] `free-trial.html` - Update branding references
- [ ] `reseller.html` - Update company name throughout
- [ ] `tutorials.html` - Update branding
- [ ] `contact.html` - Update title and content
- [ ] `client-area.html` - Update branding
- [ ] `checkout.html` - Update branding
- [ ] `admin/index.html` - Update admin panel branding

#### **3. Email Templates:**
- [ ] Update `api/contact.php` email templates
- [ ] Replace "StreamPro" with "BadBoyz" in email content

#### **4. Domain-Specific Updates:**
- [ ] Update `config.js` with actual domain name
- [ ] Update all email addresses to match your domain
- [ ] Update social media links to actual accounts

#### **5. Payment Gateway Configuration:**
- [ ] Update PayPal account settings
- [ ] Update Stripe account settings
- [ ] Test payment processing

## 🚀 **Quick Find & Replace Guide:**

### **Global Text Replacements Needed:**
```
Find: "StreamPro IPTV"
Replace: "BadBoyz IPTV"

Find: "StreamPro"
Replace: "BadBoyz"

Find: "streampro-iptv.com"
Replace: "your-actual-domain.com"

Find: "streamproiptv"
Replace: "badboyziptv"
```

### **Files to Check for Remaining References:**
1. `subscriptions.html`
2. `free-trial.html`
3. `reseller.html`
4. `tutorials.html`
5. `contact.html`
6. `client-area.html`
7. `checkout.html`
8. `admin/index.html`
9. `api/contact.php`

## 🎨 **Branding Assets Needed:**

### **Logo Requirements:**
- **Main Logo**: 200x60px (PNG with transparent background)
- **Favicon**: 32x32px (ICO format)
- **Social Media**: 1200x630px for Open Graph

### **Color Scheme:**
Current theme uses:
- **Primary**: #007bff (Blue)
- **Secondary**: #6c757d (Gray)
- **Success**: #28a745 (Green)
- **Danger**: #dc3545 (Red)

Update these in `assets/css/style.css` if needed.

## 📧 **Email Setup Checklist:**

### **Email Accounts to Create:**
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>` (optional)

### **Email Configuration:**
Update in `api/contact.php`:
```php
$config = [
    'smtp_host' => 'mail.your-domain.com',
    'smtp_username' => '<EMAIL>',
    'admin_email' => '<EMAIL>'
];
```

## 🔗 **Social Media Setup:**

### **Accounts to Create:**
- Facebook: `facebook.com/badboyziptv`
- Twitter: `twitter.com/badboyziptv`
- Instagram: `instagram.com/badboyziptv`
- YouTube: `youtube.com/badboyziptv`
- Telegram: `t.me/badboyziptv`

### **Update Links in:**
- `config.js`
- Footer sections of all HTML pages
- Contact page social media links

## 🛠 **Technical Setup:**

### **Domain Configuration:**
1. Point domain to InMotion Hosting nameservers
2. Set up SSL certificate
3. Configure email accounts
4. Test all functionality

### **Payment Gateway Setup:**
1. **PayPal:**
   - Create business account
   - Get Client ID
   - Update in `checkout.html`

2. **Stripe (Optional):**
   - Create account
   - Get API keys
   - Update in `assets/js/checkout.js`

## ✅ **Final Pre-Launch Checklist:**

- [ ] All branding updated consistently
- [ ] Logo and images replaced
- [ ] Domain and email configured
- [ ] Payment gateways tested
- [ ] SSL certificate active
- [ ] All forms working
- [ ] Mobile responsiveness verified
- [ ] Live chat functioning
- [ ] Analytics tracking added
- [ ] Backup system configured

## 🎯 **Post-Launch Tasks:**

- [ ] Submit sitemap to Google
- [ ] Set up Google Analytics
- [ ] Monitor website performance
- [ ] Test customer journey
- [ ] Set up social media accounts
- [ ] Create marketing materials
- [ ] Train support team

---

**Note:** This checklist ensures your BadBoyz IPTV website maintains consistent branding and professional appearance across all pages and functionality.
