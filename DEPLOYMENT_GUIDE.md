# StreamPro IPTV - InMotion Hosting Deployment Guide

This guide will walk you through deploying your StreamPro IPTV website on InMotion Hosting.

## 📋 Pre-Deployment Checklist

### Required Information
- [ ] InMotion Hosting account credentials
- [ ] Domain name (registered and pointed to InMotion)
- [ ] PayPal Business account
- [ ] Stripe account (optional)
- [ ] Email account for notifications
- [ ] SSL certificate (usually included with hosting)

### Files to Customize Before Upload
- [ ] Update `config.js` with your settings
- [ ] Replace logo and images in `assets/images/`
- [ ] Modify contact information throughout the site
- [ ] Update payment gateway credentials
- [ ] Customize pricing plans if needed

## 🚀 Step-by-Step Deployment

### Step 1: Prepare Your Files

1. **Download/Clone the website files**
2. **Customize branding and settings**:
   ```bash
   # Edit these key files:
   - config.js (main configuration)
   - assets/images/ (replace with your logos/images)
   - All HTML files (update company name, contact info)
   ```

3. **Update payment credentials**:
   - PayPal Client ID in `checkout.html`
   - Stripe keys in `assets/js/checkout.js`
   - Email settings in `api/contact.php`

### Step 2: Upload to InMotion Hosting

1. **Access cPanel**:
   - Log into your InMotion hosting account
   - Open cPanel from the hosting dashboard

2. **Upload files using File Manager**:
   - Open File Manager in cPanel
   - Navigate to `public_html` directory
   - Upload all website files to this directory
   - Extract if uploaded as ZIP

3. **Alternative: Upload via FTP**:
   ```bash
   # Using FileZilla or similar FTP client
   Host: your-domain.com
   Username: your-cpanel-username
   Password: your-cpanel-password
   Port: 21
   ```

### Step 3: Set File Permissions

Set proper permissions for security:
- **Directories**: 755
- **Files**: 644
- **PHP files**: 644
- **Log directories**: 755 (create if needed)

```bash
# In cPanel File Manager, select files and set permissions
# Or via SSH if available:
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;
```

### Step 4: Database Setup (Optional)

If you plan to use a database for customer management:

1. **Create MySQL Database**:
   - Go to MySQL Databases in cPanel
   - Create new database: `streampro_iptv`
   - Create database user with full privileges

2. **Import database schema**:
   ```sql
   CREATE TABLE users (
       id INT PRIMARY KEY AUTO_INCREMENT,
       email VARCHAR(255) UNIQUE NOT NULL,
       password VARCHAR(255) NOT NULL,
       first_name VARCHAR(100),
       last_name VARCHAR(100),
       plan VARCHAR(50),
       status ENUM('active', 'expired', 'suspended', 'trial') DEFAULT 'active',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       expires_at TIMESTAMP NULL,
       iptv_username VARCHAR(100),
       iptv_password VARCHAR(100)
   );

   CREATE TABLE subscriptions (
       id INT PRIMARY KEY AUTO_INCREMENT,
       user_id INT,
       plan_type VARCHAR(50),
       amount DECIMAL(10,2),
       payment_method VARCHAR(50),
       payment_id VARCHAR(255),
       status VARCHAR(50),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       FOREIGN KEY (user_id) REFERENCES users(id)
   );

   CREATE TABLE form_submissions (
       id INT PRIMARY KEY AUTO_INCREMENT,
       type ENUM('contact', 'trial', 'reseller'),
       email VARCHAR(255),
       data JSON,
       ip_address VARCHAR(45),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

### Step 5: Configure Email

1. **Set up email accounts in cPanel**:
   - Create: `<EMAIL>`
   - Create: `<EMAIL>`
   - Create: `<EMAIL>`

2. **Update email settings in `api/contact.php`**:
   ```php
   $config = [
       'smtp_host' => 'mail.your-domain.com',
       'smtp_port' => 587,
       'smtp_username' => '<EMAIL>',
       'smtp_password' => 'your-email-password',
       'admin_email' => '<EMAIL>'
   ];
   ```

### Step 6: SSL Certificate Setup

1. **Enable SSL in cPanel**:
   - Go to SSL/TLS section
   - Enable "Force HTTPS Redirect"
   - Install Let's Encrypt certificate (usually free)

2. **Update all URLs to HTTPS**:
   - Check all internal links
   - Update payment gateway URLs
   - Verify external API calls use HTTPS

### Step 7: Payment Gateway Configuration

#### PayPal Setup:
1. **Get PayPal Client ID**:
   - Log into PayPal Developer Console
   - Create new app for your domain
   - Copy Client ID

2. **Update checkout.html**:
   ```html
   <script src="https://www.paypal.com/sdk/js?client-id=YOUR_ACTUAL_CLIENT_ID&currency=USD"></script>
   ```

#### Stripe Setup (Optional):
1. **Get Stripe Keys**:
   - Log into Stripe Dashboard
   - Get Publishable Key and Secret Key

2. **Update checkout.js**:
   ```javascript
   stripe = Stripe('pk_live_YOUR_ACTUAL_PUBLISHABLE_KEY');
   ```

### Step 8: Test Everything

1. **Test all pages load correctly**
2. **Test contact forms**:
   - Contact form submission
   - Free trial request
   - Reseller application

3. **Test payment flow**:
   - Add item to cart
   - Proceed to checkout
   - Test payment (use test mode first)

4. **Test responsive design**:
   - Mobile devices
   - Tablets
   - Different browsers

### Step 9: SEO and Analytics Setup

1. **Google Analytics**:
   - Create GA4 property
   - Add tracking code to all pages
   - Set up conversion goals

2. **Google Search Console**:
   - Verify domain ownership
   - Submit sitemap
   - Monitor for errors

3. **Create sitemap.xml**:
   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
       <url><loc>https://your-domain.com/</loc></url>
       <url><loc>https://your-domain.com/subscriptions.html</loc></url>
       <url><loc>https://your-domain.com/free-trial.html</loc></url>
       <url><loc>https://your-domain.com/reseller.html</loc></url>
       <url><loc>https://your-domain.com/tutorials.html</loc></url>
       <url><loc>https://your-domain.com/about.html</loc></url>
       <url><loc>https://your-domain.com/contact.html</loc></url>
   </urlset>
   ```

### Step 10: Security Hardening

1. **Create .htaccess file**:
   ```apache
   # Force HTTPS
   RewriteEngine On
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

   # Security headers
   Header always set X-Content-Type-Options nosniff
   Header always set X-Frame-Options DENY
   Header always set X-XSS-Protection "1; mode=block"

   # Hide sensitive files
   <Files "config.js">
       Order allow,deny
       Deny from all
   </Files>

   <Files "*.log">
       Order allow,deny
       Deny from all
   </Files>
   ```

2. **Create logs directory**:
   ```bash
   mkdir logs
   chmod 755 logs
   ```

3. **Secure admin panel**:
   - Add password protection to `/admin/` directory
   - Use strong passwords
   - Consider IP restrictions

## 🔧 Post-Deployment Configuration

### Monitor and Maintain

1. **Set up monitoring**:
   - Uptime monitoring (UptimeRobot, Pingdom)
   - Error logging and alerts
   - Performance monitoring

2. **Regular backups**:
   - Enable automatic backups in cPanel
   - Download local copies regularly
   - Test backup restoration

3. **Security updates**:
   - Keep all scripts updated
   - Monitor for security vulnerabilities
   - Regular security scans

### Performance Optimization

1. **Enable caching**:
   - Browser caching via .htaccess
   - Enable gzip compression
   - Optimize images

2. **CDN setup** (optional):
   - CloudFlare (free tier available)
   - Configure for static assets

## 🚨 Troubleshooting Common Issues

### Email Not Working
- Check SMTP settings in cPanel
- Verify email account exists
- Test with simple PHP mail script
- Check spam folders

### Payment Issues
- Verify API keys are correct
- Check sandbox vs production mode
- Test with small amounts first
- Monitor payment gateway logs

### SSL Certificate Problems
- Force HTTPS redirect
- Check mixed content warnings
- Verify certificate installation
- Clear browser cache

### Performance Issues
- Optimize images (compress, resize)
- Enable gzip compression
- Minimize CSS/JS files
- Use browser caching

## 📞 Support Resources

- **InMotion Hosting Support**: Available 24/7
- **PayPal Developer Support**: developer.paypal.com
- **Stripe Support**: stripe.com/support
- **General Web Development**: Stack Overflow, MDN

## ✅ Go-Live Checklist

- [ ] All files uploaded and permissions set
- [ ] Database configured (if using)
- [ ] Email system working
- [ ] SSL certificate installed and working
- [ ] Payment gateways configured and tested
- [ ] All forms tested and working
- [ ] Analytics tracking installed
- [ ] SEO elements in place
- [ ] Security measures implemented
- [ ] Backup system configured
- [ ] Monitoring tools set up
- [ ] Domain DNS properly configured
- [ ] All links working correctly
- [ ] Mobile responsiveness verified
- [ ] Cross-browser compatibility tested

## 🎉 Congratulations!

Your StreamPro IPTV website is now live and ready to accept customers. Remember to:

1. **Test everything thoroughly** before promoting
2. **Monitor performance and errors** regularly
3. **Keep backups** of your site
4. **Update content** and pricing as needed
5. **Provide excellent customer support**

Your professional IPTV selling website is now ready to generate revenue!