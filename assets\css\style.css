/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.navbar {
    padding: 1rem 0;
}

.navbar-brand .logo {
    height: 50px;
    width: auto;
}

.navbar-nav .nav-link {
    color: #333 !important;
    font-weight: 500;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #007bff !important;
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    color: #007bff !important;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.4);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #333;
    padding-top: 100px;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../images/hero-bg.jpg') center/cover;
    opacity: 0.1;
    z-index: -1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #007bff 0%, #00d4ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-features {
    list-style: none;
    margin: 2rem 0;
}

.hero-features li {
    padding: 10px 0;
    font-size: 1.2rem;
    font-weight: 500;
}

.hero-features i {
    color: #28a745;
    margin-right: 10px;
    font-size: 1.1rem;
}

.hero-buttons {
    margin-top: 3rem;
}

.hero-buttons .btn {
    margin: 10px 5px;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 30px;
}

.btn-outline-light {
    border: 2px solid #fff;
    color: #fff;
    background: transparent;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: #fff;
    color: #333;
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Features Section */
.features-section {
    background: #ffffff;
    color: #333;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 1rem;
}

.highlight-text {
    font-size: 1.3rem;
    font-weight: 600;
    color: #007bff;
}

/* Pricing Section */
.pricing-section {
    background: #ffffff;
    color: #333;
}

.pricing-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,123,255,0.2);
}

.pricing-card.popular {
    border: 2px solid #007bff;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 5px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.pricing-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.price {
    margin-bottom: 2rem;
}

.price .currency {
    font-size: 1.5rem;
    vertical-align: top;
    color: #007bff;
}

.price .amount {
    font-size: 3rem;
    font-weight: 800;
    color: #007bff;
}

.pricing-features {
    list-style: none;
    margin: 2rem 0;
    text-align: left;
}

.pricing-features li {
    padding: 8px 0;
    font-size: 0.95rem;
    color: #666;
}

.pricing-features i {
    color: #28a745;
    margin-right: 10px;
    width: 15px;
}

.btn-block {
    width: 100%;
    padding: 12px;
    font-weight: 600;
    margin-top: 1rem;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #333;
    padding: 120px 0 60px;
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: #ccc;
}

/* Subscription Plans */
.subscription-plans {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
}

.price-period {
    color: #ccc;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.savings {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 10px;
    display: inline-block;
}

/* Features Detailed */
.features-detailed {
    background: #111;
}

.feature-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,123,255,0.2);
}

.feature-icon {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.feature-card h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #ccc;
}

/* Trial Form */
.trial-form-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.trial-card {
    background: rgba(255,255,255,0.05);
    border-radius: 20px;
    padding: 3rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.trial-icon {
    font-size: 4rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.trial-form .form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #fff;
    border-radius: 10px;
}

.trial-form .form-control:focus {
    background: rgba(255,255,255,0.15);
    border-color: #007bff;
    color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.trial-form .form-label {
    color: #fff;
    font-weight: 500;
}

.trial-form .form-check-label {
    color: #ccc;
}

/* Trial Benefits */
.trial-benefits {
    background: #111;
}

.benefit-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,123,255,0.2);
}

.benefit-icon {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.benefit-card h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.benefit-card p {
    color: #ccc;
}

/* How It Works */
.how-it-works {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
}

.step-card {
    text-align: center;
    padding: 2rem;
}

.step-number {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.step-card h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.step-card p {
    color: #ccc;
}

/* FAQ */
.faq-section {
    background: #111;
}

.faq-item {
    background: rgba(255,255,255,0.05);
    border-radius: 10px;
    margin-bottom: 1rem;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.1);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: rgba(255,255,255,0.1);
}

.faq-question h5 {
    margin: 0;
    color: #fff;
    font-weight: 500;
}

.faq-question i {
    color: #007bff;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(0,0,0,0.3);
}

.faq-answer p {
    padding: 1.5rem;
    margin: 0;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
    }

    .pricing-card.popular {
        transform: none;
        margin-top: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .trial-card {
        padding: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-features {
        font-size: 1rem;
    }

    .pricing-card {
        margin-bottom: 2rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .trial-card {
        padding: 1.5rem;
    }
}

/* Reseller Styles */
.reseller-hero {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    color: white;
    padding: 120px 0 60px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.stat p {
    color: #ccc;
    font-size: 0.9rem;
}

.reseller-benefits {
    background: #111;
}

.reseller-plans {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.reseller-card {
    background: rgba(255,255,255,0.05);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.reseller-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,123,255,0.2);
}

.reseller-card.popular {
    border: 2px solid #007bff;
    transform: scale(1.05);
}

.plan-header h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #fff;
}

.credits {
    color: #28a745;
    font-weight: 600;
    margin-top: 0.5rem;
}

.application-form {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
}

.form-card {
    background: rgba(255,255,255,0.05);
    border-radius: 20px;
    padding: 3rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

/* Tutorials Styles */
.device-categories {
    background: #111;
}

.device-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
}

.device-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,123,255,0.2);
}

.device-icon {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.device-card h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.device-card p {
    color: #ccc;
    margin-bottom: 1rem;
}

.device-apps {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.app-badge {
    background: rgba(0,123,255,0.2);
    color: #007bff;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tutorial-content {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
}

.tutorial-section {
    margin-bottom: 4rem;
}

.tutorial-title {
    color: #fff;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.tutorial-title i {
    color: #007bff;
    margin-right: 1rem;
}

.tutorial-steps {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.step {
    display: flex;
    margin-bottom: 2rem;
    align-items: flex-start;
}

.step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.step-content h4 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #ccc;
    margin-bottom: 0.5rem;
}

.step-content ul {
    color: #ccc;
    margin-left: 1rem;
}

.video-placeholder {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 3rem 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.video-placeholder i {
    font-size: 4rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.video-placeholder p {
    color: #fff;
    margin-bottom: 1rem;
}

.download-links h5 {
    color: #fff;
    margin-bottom: 1rem;
}

.app-download-btn {
    display: block;
    background: rgba(255,255,255,0.05);
    color: #fff;
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.1);
}

.app-download-btn:hover {
    background: rgba(0,123,255,0.2);
    color: #007bff;
    text-decoration: none;
}

.app-download-btn i {
    margin-right: 0.5rem;
}

.supported-brands h5 {
    color: #fff;
    margin-bottom: 1rem;
}

.brand-logos {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.brand-logo {
    background: rgba(255,255,255,0.1);
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 500;
}

.support-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    text-align: center;
}

.support-buttons {
    margin-top: 2rem;
}

/* Contact Styles */
.contact-methods {
    background: #ffffff;
}

.contact-card {
    background: #ffffff;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,123,255,0.2);
}

.contact-icon {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.contact-card h4 {
    color: #333;
    margin-bottom: 1rem;
}

.contact-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.contact-form-section {
    background: #ffffff;
}

.contact-form-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 3rem;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-form .form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #fff;
    border-radius: 10px;
}

.contact-form .form-control:focus {
    background: rgba(255,255,255,0.15);
    border-color: #007bff;
    color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.contact-form .form-label {
    color: #fff;
    font-weight: 500;
}

.contact-form .form-check-label {
    color: #ccc;
}

.support-hours {
    background: #111;
}

.hours-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.hours-item {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.hours-item h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.hours-item p {
    color: #007bff;
    font-weight: 600;
}

/* Live Chat Widget */
.live-chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10000;
    font-family: 'Poppins', sans-serif;
}

.chat-button {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    animation: pulse 2s infinite;
}

.chat-button:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0,123,255,0.6);
}

.chat-button i {
    font-size: 1.8rem;
}

.chat-button .notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

@keyframes pulse {
    0% {
        box-shadow: 0 6px 20px rgba(0,123,255,0.4);
    }
    50% {
        box-shadow: 0 6px 20px rgba(0,123,255,0.4), 0 0 0 10px rgba(0,123,255,0.1);
    }
    100% {
        box-shadow: 0 6px 20px rgba(0,123,255,0.4);
    }
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 380px;
    height: 500px;
    background: rgba(26, 26, 46, 0.98);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255,255,255,0.1);
    display: none;
    flex-direction: column;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.chat-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
}

.chat-header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.chat-header-info h5 {
    color: #fff;
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.chat-header-info p {
    color: rgba(255,255,255,0.8);
    margin: 0;
    font-size: 0.8rem;
}

.close-chat {
    background: rgba(255,255,255,0.1);
    border: none;
    color: #fff;
    font-size: 1.2rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.close-chat:hover {
    background: rgba(255,255,255,0.2);
}

.chat-body {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.chat-body::-webkit-scrollbar {
    width: 6px;
}

.chat-body::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb {
    background: rgba(0,123,255,0.5);
    border-radius: 3px;
}

.chat-message {
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

.chat-message.bot {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.chat-message.bot .message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    flex-shrink: 0;
}

.chat-message.bot p {
    background: rgba(0,123,255,0.15);
    color: #fff;
    padding: 1rem;
    border-radius: 18px 18px 18px 6px;
    margin: 0;
    max-width: 250px;
    line-height: 1.4;
    border: 1px solid rgba(0,123,255,0.2);
}

.chat-message.user {
    display: flex;
    justify-content: flex-end;
}

.chat-message.user p {
    background: rgba(255,255,255,0.1);
    color: #fff;
    padding: 1rem;
    border-radius: 18px 18px 6px 18px;
    margin: 0;
    max-width: 250px;
    line-height: 1.4;
    border: 1px solid rgba(255,255,255,0.1);
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.typing-indicator .message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
}

.typing-dots {
    background: rgba(0,123,255,0.15);
    padding: 1rem;
    border-radius: 18px 18px 18px 6px;
    border: 1px solid rgba(0,123,255,0.2);
}

.typing-dots span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #007bff;
    margin: 0 2px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chat-input {
    padding: 1.5rem;
    background: rgba(26, 26, 46, 0.95);
    border-top: 1px solid rgba(255,255,255,0.1);
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.chat-input input {
    flex: 1;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #fff;
    border-radius: 25px;
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.chat-input input:focus {
    outline: none;
    border-color: #007bff;
    background: rgba(255,255,255,0.15);
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.chat-input input::placeholder {
    color: rgba(255,255,255,0.5);
}

.chat-send-btn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: white;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.chat-send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,123,255,0.4);
}

.chat-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.quick-replies {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.quick-reply {
    background: rgba(0,123,255,0.1);
    border: 1px solid rgba(0,123,255,0.3);
    color: #007bff;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-reply:hover {
    background: rgba(0,123,255,0.2);
    border-color: #007bff;
}

/* Client Area Styles */
.login-section {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.login-card {
    background: rgba(255,255,255,0.05);
    border-radius: 20px;
    padding: 3rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.login-icon {
    font-size: 4rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.login-form .form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #fff;
    border-radius: 10px;
}

.login-form .form-control:focus {
    background: rgba(255,255,255,0.15);
    border-color: #007bff;
    color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.login-form .form-label {
    color: #fff;
    font-weight: 500;
}

.login-form .form-check-label {
    color: #ccc;
}

.login-footer a {
    color: #007bff;
    text-decoration: none;
}

.login-footer a:hover {
    text-decoration: underline;
}

.dashboard-section {
    background: #ffffff;
    min-height: 100vh;
    padding-top: 100px;
    color: #333;
}

.welcome-header {
    background: #ffffff;
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-header h2 {
    color: #333;
    margin: 0;
}

.welcome-header p {
    color: #666;
    margin: 0;
}

.stat-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2rem;
    color: #007bff;
}

.stat-info h4 {
    color: #fff;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-info p {
    color: #ccc;
    margin: 0;
    font-size: 0.9rem;
}

.dashboard-card {
    background: #ffffff;
    border-radius: 15px;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.dashboard-card .card-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.dashboard-card .card-header h3 {
    color: #333;
    margin: 0;
    font-size: 1.3rem;
}

.dashboard-card .card-header i {
    color: #007bff;
    margin-right: 0.5rem;
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

.info-item {
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item label {
    color: #666;
    font-weight: 500;
    margin: 0;
}

.info-item span {
    color: #333;
}

.credential {
    font-family: 'Courier New', monospace;
    background: rgba(0,0,0,0.3);
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.9rem;
}

.status-active {
    color: #28a745 !important;
    font-weight: 600;
}

.connection-info {
    background: rgba(0,0,0,0.2);
    border-radius: 10px;
    padding: 1.5rem;
}

.app-downloads {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.app-download-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255,255,255,0.05);
    padding: 1rem;
    border-radius: 10px;
    text-decoration: none;
    color: #fff;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.1);
}

.app-download-item:hover {
    background: rgba(0,123,255,0.1);
    color: #007bff;
    text-decoration: none;
}

.app-download-item i:first-child {
    font-size: 2rem;
    color: #007bff;
}

.app-download-item div {
    flex: 1;
}

.app-download-item h5 {
    margin: 0;
    font-size: 1rem;
}

.app-download-item p {
    margin: 0;
    font-size: 0.8rem;
    color: #ccc;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.btn-block {
    width: 100%;
}

/* Connection Tabs */
.connection-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.connection-tab {
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.2);
    color: #ccc;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1rem;
}

.connection-tab:hover {
    background: rgba(0,123,255,0.2);
    border-color: rgba(0,123,255,0.5);
    color: #007bff;
}

.connection-tab.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.4);
}

/* Connections Badge */
.connections-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: inline-block;
}

.connections-badge.premium {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    color: #000;
}

/* Pricing Grid Animation */
.pricing-grid {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Checkout Styles */
.checkout-section {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    min-height: 100vh;
    padding-top: 100px;
}

.order-summary {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    position: sticky;
    top: 120px;
}

.order-summary h3 {
    color: #fff;
    margin-bottom: 1.5rem;
}

.order-summary h3 i {
    color: #007bff;
    margin-right: 0.5rem;
}

.selected-plan {
    background: rgba(0,0,0,0.2);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.plan-details h4 {
    color: #fff;
    margin-bottom: 1rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
}

.plan-features li {
    color: #ccc;
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

.plan-features i {
    color: #28a745;
    margin-right: 0.5rem;
    width: 15px;
}

.price-breakdown {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 1rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    color: #ccc;
}

.price-row.total {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    color: #fff;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.security-badge {
    height: 40px;
    opacity: 0.7;
}

.checkout-form {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.checkout-form h3 {
    color: #fff;
    margin-bottom: 1.5rem;
}

.checkout-form h3 i {
    color: #007bff;
    margin-right: 0.5rem;
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.form-section h4 {
    color: #fff;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.checkout-form .form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #fff;
    border-radius: 10px;
}

.checkout-form .form-control:focus {
    background: rgba(255,255,255,0.15);
    border-color: #007bff;
    color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.checkout-form .form-label {
    color: #fff;
    font-weight: 500;
}

.checkout-form .form-check-label {
    color: #ccc;
}

.payment-methods {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.payment-method {
    flex: 1;
    background: rgba(255,255,255,0.05);
    border: 2px solid rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method:hover {
    background: rgba(0,123,255,0.1);
    border-color: rgba(0,123,255,0.3);
}

.payment-method.active {
    background: rgba(0,123,255,0.2);
    border-color: #007bff;
}

.payment-method i {
    font-size: 2rem;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.payment-method span {
    color: #fff;
    font-weight: 500;
}

.payment-form {
    display: none;
}

.payment-form.active {
    display: block;
}

.paypal-info {
    background: rgba(0,0,0,0.2);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.paypal-info p {
    color: #ccc;
    margin: 0.5rem 0;
}

.paypal-info i {
    color: #28a745;
    margin-right: 0.5rem;
}

.stripe-element {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

#card-errors {
    color: #dc3545;
    margin-top: 0.5rem;
}

.crypto-info {
    background: rgba(0,0,0,0.2);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.crypto-info p {
    color: #ccc;
    margin: 0.5rem 0;
}

.crypto-info i {
    color: #f7931a;
    margin-right: 0.5rem;
}

.crypto-options {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.crypto-option {
    background: rgba(255,255,255,0.05);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    flex: 1;
    color: #fff;
    border: 1px solid rgba(255,255,255,0.1);
}

.crypto-option i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.security-info {
    background: rgba(0,0,0,0.2);
    border-radius: 10px;
    padding: 2rem;
    margin-top: 2rem;
}

.security-icon {
    font-size: 2rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.security-info h6 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.security-info p {
    color: #ccc;
    margin: 0;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background: #f8f9fa;
    color: #333;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

.footer p {
    margin: 0;
}

/* Streaming Channels Showcase */
.streaming-channels {
    background: #ffffff;
    position: relative;
    overflow: hidden;
    min-height: 600px;
}

.channels-container {
    position: relative;
    height: 500px;
    border-radius: 20px;
    background: rgba(255,255,255,0.02);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    overflow: hidden;
}

.floating-channels {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.channel-bubble {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.1);
}

.channel-bubble:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 40px rgba(0,0,0,0.4);
}

.channel-logo {
    text-align: center;
    width: 100%;
}

.channel-name {
    font-weight: 700;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
    display: block;
}

/* Channel-specific styling */
.netflix {
    background: linear-gradient(135deg, #e50914 0%, #b20710 100%);
    color: white;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.hulu {
    background: linear-gradient(135deg, #1ce783 0%, #17c671 100%);
    color: white;
    top: 20%;
    right: 10%;
    animation-delay: 0.5s;
}

.disney {
    background: linear-gradient(135deg, #113ccf 0%, #0e2fa3 100%);
    color: white;
    top: 5%;
    left: 25%;
    animation-delay: 1s;
}

.hbo {
    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
    color: white;
    top: 35%;
    left: 8%;
    animation-delay: 1.5s;
}

.prime {
    background: linear-gradient(135deg, #00a8e1 0%, #0073a8 100%);
    color: white;
    top: 15%;
    right: 25%;
    animation-delay: 2s;
}

.apple {
    background: linear-gradient(135deg, #000000 0%, #333333 100%);
    color: white;
    top: 45%;
    right: 5%;
    animation-delay: 2.5s;
}

.paramount {
    background: linear-gradient(135deg, #0064ff 0%, #0052cc 100%);
    color: white;
    top: 60%;
    left: 15%;
    animation-delay: 3s;
}

.peacock {
    background: linear-gradient(135deg, #000000 0%, #333333 100%);
    color: #ffd700;
    top: 25%;
    left: 45%;
    animation-delay: 3.5s;
}

.nickelodeon {
    background: linear-gradient(135deg, #ff6600 0%, #e55a00 100%);
    color: white;
    top: 70%;
    right: 20%;
    animation-delay: 4s;
}

.nickjr {
    background: linear-gradient(135deg, #0099cc 0%, #007aa3 100%);
    color: white;
    top: 50%;
    left: 35%;
    animation-delay: 4.5s;
}

.cartoon {
    background: linear-gradient(135deg, #000000 0%, #333333 100%);
    color: #ffff00;
    top: 80%;
    left: 5%;
    animation-delay: 5s;
}

.discovery {
    background: linear-gradient(135deg, #0077be 0%, #005a8b 100%);
    color: white;
    top: 40%;
    right: 35%;
    animation-delay: 5.5s;
}

.espn {
    background: linear-gradient(135deg, #d50000 0%, #b71c1c 100%);
    color: white;
    top: 65%;
    left: 55%;
    animation-delay: 6s;
}

.fox {
    background: linear-gradient(135deg, #003366 0%, #002244 100%);
    color: white;
    top: 30%;
    right: 50%;
    animation-delay: 6.5s;
}

.cnn {
    background: linear-gradient(135deg, #cc0000 0%, #990000 100%);
    color: white;
    top: 85%;
    right: 40%;
    animation-delay: 7s;
}

.bbc {
    background: linear-gradient(135deg, #000000 0%, #333333 100%);
    color: white;
    top: 75%;
    right: 8%;
    animation-delay: 7.5s;
}

/* Floating animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(1deg);
    }
    50% {
        transform: translateY(-10px) rotate(-1deg);
    }
    75% {
        transform: translateY(-15px) rotate(0.5deg);
    }
}

/* Overlay content */
.channels-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
    background: rgba(255,255,255,0.95);
    padding: 3rem;
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.overlay-content h3 {
    color: #333;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.overlay-content p {
    color: #666;
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.overlay-content .btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 25px;
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
    transition: all 0.3s ease;
}

.overlay-content .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0,123,255,0.4);
}

/* Channel Notification */
.channel-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 1.5rem;
    max-width: 350px;
    z-index: 10000;
    animation: slideInRight 0.3s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.notification-content h4 {
    color: #007bff;
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.notification-content p {
    color: #fff;
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-content small {
    color: #ccc;
    font-size: 0.8rem;
}

.close-notification {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: #ccc;
    font-size: 1rem;
    cursor: pointer;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-notification:hover {
    background: rgba(255,255,255,0.1);
    color: #fff;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Popular Services Section */
.popular-services {
    background: #ffffff;
}

.service-card {
    background: #ffffff;
    border-radius: 15px;
    padding: 2rem 1rem;
    text-align: center;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,123,255,0.2);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-logo {
    font-weight: 700;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
    position: relative;
    z-index: 1;
}

/* Service-specific colors */
.service-card.netflix {
    background: linear-gradient(135deg, rgba(229, 9, 20, 0.2) 0%, rgba(178, 7, 16, 0.2) 100%);
    border-color: rgba(229, 9, 20, 0.3);
}

.service-card.netflix .service-logo {
    color: #e50914;
}

.service-card.hulu {
    background: linear-gradient(135deg, rgba(28, 231, 131, 0.2) 0%, rgba(23, 198, 113, 0.2) 100%);
    border-color: rgba(28, 231, 131, 0.3);
}

.service-card.hulu .service-logo {
    color: #1ce783;
}

.service-card.disney {
    background: linear-gradient(135deg, rgba(17, 60, 207, 0.2) 0%, rgba(14, 47, 163, 0.2) 100%);
    border-color: rgba(17, 60, 207, 0.3);
}

.service-card.disney .service-logo {
    color: #113ccf;
}

.service-card.hbo {
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.2) 0%, rgba(91, 33, 182, 0.2) 100%);
    border-color: rgba(124, 58, 237, 0.3);
}

.service-card.hbo .service-logo {
    color: #7c3aed;
}

.service-card.prime {
    background: linear-gradient(135deg, rgba(0, 168, 225, 0.2) 0%, rgba(0, 115, 168, 0.2) 100%);
    border-color: rgba(0, 168, 225, 0.3);
}

.service-card.prime .service-logo {
    color: #00a8e1;
}

.service-card.apple {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(51, 51, 51, 0.2) 100%);
    border-color: rgba(255, 255, 255, 0.3);
}

.service-card.apple .service-logo {
    color: #fff;
}

.service-card.paramount {
    background: linear-gradient(135deg, rgba(0, 100, 255, 0.2) 0%, rgba(0, 82, 204, 0.2) 100%);
    border-color: rgba(0, 100, 255, 0.3);
}

.service-card.paramount .service-logo {
    color: #0064ff;
}

.service-card.peacock {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(51, 51, 51, 0.2) 100%);
    border-color: rgba(255, 215, 0, 0.3);
}

.service-card.peacock .service-logo {
    color: #ffd700;
}

.service-card.nickelodeon {
    background: linear-gradient(135deg, rgba(255, 102, 0, 0.2) 0%, rgba(229, 90, 0, 0.2) 100%);
    border-color: rgba(255, 102, 0, 0.3);
}

.service-card.nickelodeon .service-logo {
    color: #ff6600;
}

.service-card.nickjr {
    background: linear-gradient(135deg, rgba(0, 153, 204, 0.2) 0%, rgba(0, 122, 163, 0.2) 100%);
    border-color: rgba(0, 153, 204, 0.3);
}

.service-card.nickjr .service-logo {
    color: #0099cc;
}

.service-card.espn {
    background: linear-gradient(135deg, rgba(213, 0, 0, 0.2) 0%, rgba(183, 28, 28, 0.2) 100%);
    border-color: rgba(213, 0, 0, 0.3);
}

.service-card.espn .service-logo {
    color: #d50000;
}

.service-card.cnn {
    background: linear-gradient(135deg, rgba(204, 0, 0, 0.2) 0%, rgba(153, 0, 0, 0.2) 100%);
    border-color: rgba(204, 0, 0, 0.3);
}

.service-card.cnn .service-logo {
    color: #cc0000;
}

/* More Streams Section */
.more-streams-section {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 3rem 2rem;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.more-streams-section h4 {
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.more-streams-section p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.more-streams-section .btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.more-streams-section .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.3);
}

/* White Theme - Ensure all pages are white with visible text */
.container, .container-fluid {
    color: #333;
}

/* All sections white background */
section {
    background: #ffffff !important;
    color: #333 !important;
}

/* All headings visible */
h1, h2, h3, h4, h5, h6 {
    color: #333 !important;
}

/* All paragraphs and text visible */
p, span, div {
    color: #333;
}

/* Muted text */
.text-muted {
    color: #666 !important;
}

/* Cards and content areas */
.card, .content-area, .main-content {
    background: #ffffff !important;
    color: #333 !important;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Form elements */
.form-control, .form-select {
    background: #ffffff;
    color: #333;
    border: 1px solid #ddd;
}

.form-control:focus, .form-select:focus {
    background: #ffffff;
    color: #333;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Tables */
.table {
    color: #333;
    background: #ffffff;
}

.table th {
    background: #f8f9fa;
    color: #333;
    border-color: #dee2e6;
}

.table td {
    color: #333;
    border-color: #dee2e6;
}

/* Alerts */
.alert {
    color: #333;
}

/* List groups */
.list-group-item {
    background: #ffffff;
    color: #333;
    border-color: #dee2e6;
}

/* Modals */
.modal-content {
    background: #ffffff;
    color: #333;
}

.modal-header {
    background: #f8f9fa;
    color: #333;
    border-bottom: 1px solid #dee2e6;
}

/* Breadcrumbs */
.breadcrumb {
    background: #f8f9fa;
}

.breadcrumb-item a {
    color: #007bff;
}

/* Ensure all content is visible */
.content, .main, .wrapper {
    background: #ffffff;
    color: #333;
}

/* Homepage Additional Sections */
.about-section {
    background: #ffffff;
    color: #333;
}

.about-content {
    color: #fff;
}

.about-content h2 {
    color: #fff;
    margin-bottom: 2rem;
}

.about-content p {
    color: #ccc;
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.stats-row {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #ccc;
    font-size: 0.9rem;
    margin: 0;
}

.coverage-section {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
}

.world-map {
    text-align: center;
    margin-top: 2rem;
}

.world-map img {
    max-width: 100%;
    height: auto;
    opacity: 0.8;
}

.why-choose-section {
    background: #111;
}

.devices-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.devices-content {
    color: #fff;
}

.devices-content h2 {
    color: #fff;
    margin-bottom: 2rem;
}

.device-category {
    background: rgba(255,255,255,0.05);
    border-radius: 10px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.device-category h5 {
    color: #fff;
    margin-bottom: 1rem;
}

.device-category h5 i {
    color: #007bff;
    margin-right: 0.75rem;
}

.device-category p {
    color: #ccc;
    margin: 0;
    line-height: 1.6;
}

.devices-image {
    text-align: center;
}

.devices-image img {
    max-width: 100%;
    height: auto;
    animation: float 3s ease-in-out infinite;
}

/* Counter Animation */
.counter {
    display: inline-block;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .payment-methods {
        flex-direction: column;
    }

    .crypto-options {
        flex-direction: column;
    }

    .welcome-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .chat-window {
        width: 300px;
        height: 350px;
    }

    .stats-row {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .device-category {
        margin-bottom: 1rem;
    }

    .connection-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .connection-tab {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Live Chat Mobile Responsive */
    .live-chat-widget {
        bottom: 15px;
        right: 15px;
    }

    .chat-button {
        width: 60px;
        height: 60px;
    }

    .chat-button i {
        font-size: 1.5rem;
    }

    .chat-window {
        width: calc(100vw - 30px);
        height: 70vh;
        bottom: 75px;
        right: -15px;
        border-radius: 15px;
    }

    .chat-header {
        padding: 1rem;
    }

    .chat-header-info h5 {
        font-size: 1rem;
    }

    .chat-header-info p {
        font-size: 0.75rem;
    }

    .chat-body {
        padding: 1rem;
    }

    .chat-message.bot p,
    .chat-message.user p {
        padding: 0.75rem;
        font-size: 0.9rem;
        max-width: 200px;
    }

    .quick-replies {
        flex-direction: column;
        gap: 0.25rem;
    }

    .quick-reply {
        padding: 0.4rem 0.8rem;
        font-size: 0.75rem;
        text-align: center;
    }

    .chat-input {
        padding: 1rem;
    }

    .chat-input input {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .chat-send-btn {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    /* Streaming Channels Mobile */
    .streaming-channels {
        min-height: 400px;
    }

    .channels-container {
        height: 350px;
    }

    .channel-bubble {
        width: 80px;
        height: 80px;
    }

    .channel-name {
        font-size: 0.6rem;
    }

    .channels-overlay {
        padding: 2rem 1rem;
    }

    .overlay-content h3 {
        font-size: 1.8rem;
    }

    .overlay-content p {
        font-size: 1rem;
    }

    .overlay-content .btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    /* Adjust channel positions for mobile */
    .netflix { top: 5%; left: 5%; }
    .hulu { top: 15%; right: 5%; }
    .disney { top: 25%; left: 15%; }
    .hbo { top: 35%; right: 15%; }
    .prime { top: 45%; left: 25%; }
    .apple { top: 55%; right: 25%; }
    .paramount { top: 65%; left: 35%; }
    .peacock { top: 75%; right: 35%; }
    .nickelodeon { top: 85%; left: 45%; }
    .nickjr { top: 10%; right: 45%; }
    .cartoon { top: 20%; left: 55%; }
    .discovery { top: 30%; right: 55%; }
    .espn { top: 40%; left: 65%; }
    .fox { top: 50%; right: 65%; }
    .cnn { top: 60%; left: 75%; }
    .bbc { top: 70%; right: 75%; }
}
