<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test - BadBoyz IPTV</title>
    
    <!-- Test CSS Loading -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        /* Inline CSS for testing */
        .test-container {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            margin: 2rem;
            border-radius: 15px;
        }
        
        .css-loaded {
            display: none; /* This will be overridden if CSS loads */
        }
        
        .css-not-loaded {
            background: #dc3545;
            color: white;
            padding: 1rem;
            text-align: center;
            margin: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CSS Loading Test</h1>
        <p>If you see this styled correctly, basic CSS is working.</p>
    </div>
    
    <!-- This will show if external CSS loads -->
    <div class="hero-section css-loaded">
        <h2>✅ External CSS Loaded Successfully!</h2>
        <p>Your style.css file is loading correctly.</p>
    </div>
    
    <!-- This will show if external CSS doesn't load -->
    <div class="css-not-loaded">
        <h2>❌ External CSS Not Loading</h2>
        <p>There's an issue with your style.css file.</p>
    </div>
    
    <div class="test-container">
        <h3>Troubleshooting Steps:</h3>
        <ol style="text-align: left; max-width: 600px; margin: 0 auto;">
            <li>Check file path: assets/css/style.css</li>
            <li>Verify file permissions (644 for files, 755 for folders)</li>
            <li>Clear browser cache (Ctrl+F5)</li>
            <li>Check if file uploaded completely</li>
            <li>Verify file size is not 0 bytes</li>
        </ol>
    </div>
    
    <script>
        // Check if CSS classes are applied
        window.addEventListener('load', function() {
            const heroSection = document.querySelector('.hero-section');
            const notLoadedDiv = document.querySelector('.css-not-loaded');
            
            if (heroSection && window.getComputedStyle(heroSection).display !== 'none') {
                notLoadedDiv.style.display = 'none';
            } else {
                console.log('CSS not loading properly');
            }
        });
    </script>
</body>
</html>
